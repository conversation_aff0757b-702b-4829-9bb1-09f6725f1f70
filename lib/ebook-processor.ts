import { createSupabaseServerClient } from '@/lib/supabase/client'

export interface Chapter {
  title: string
  content: string
  chapter_number: number
  word_count: number
}

export interface ProcessingResult {
  chapters: Chapter[]
  wordCount: number
  readingTimeMinutes: number
  pageCount: number
  metadata?: {
    title?: string
    author?: string
    description?: string
  }
}

/**
 * Main entry point for processing ebook files from URL
 */
export async function processEbook(projectId: string, fileUrl: string, fileType: 'pdf' | 'epub'): Promise<ProcessingResult> {
  try {
    console.log(`Processing ${fileType.toUpperCase()} from URL: ${fileUrl}`)

    if (fileType === 'pdf') {
      throw new Error('PDF files must be converted to EPUB format first using the chapter marking tool.')
    }

    // Download the file
    const response = await fetch(fileUrl)
    if (!response.ok) {
      throw new Error(`Failed to download file: ${response.statusText}`)
    }

    const buffer = await response.arrayBuffer()
    console.log('Buffer size:', buffer.byteLength)

    return await processEPUB(Buffer.from(buffer), false) // Full processing
  } catch (error) {
    console.error('Error processing ebook:', error)
    throw error
  }
}

/**
 * Process ebook buffer directly
 */
export async function processEbookBuffer(buffer: Buffer, fileType: 'pdf' | 'epub', previewOnly: boolean = false): Promise<ProcessingResult> {
  console.log(`Processing ${fileType.toUpperCase()} buffer${previewOnly ? ' (PREVIEW MODE - Chapter 1 only)' : ''}`)

  if (fileType === 'pdf') {
    return await processPDF(buffer, previewOnly)
  }

  return await processEPUB(buffer, previewOnly)
}

/**
 * Process EPUB files using epub2 library
 */
async function processEPUB(buffer: Buffer, previewOnly: boolean = false): Promise<ProcessingResult> {
  try {
    const { WordTokenizer } = await import('natural')
    const readingTime = (await import('reading-time')).default
    const EPub = (await import('epub2')).EPub

    const wordTokenizer = new WordTokenizer()

    console.log('Processing EPUB buffer, size:', buffer.length)

    // Parse EPUB
    const epub = await EPub.fromBuffer(buffer)
    console.log('EPUB parsed successfully')

    // Extract metadata
    const metadata = {
      title: epub.metadata.title || 'Untitled Book',
      author: epub.metadata.creator || 'Unknown Author',
      description: epub.metadata.description || ''
    }

    console.log('EPUB metadata:', metadata)

    // Get chapters from spine
    const spineItems = epub.flow
    console.log(`Found ${spineItems.length} spine items`)

    const chapters: Chapter[] = []
    let totalWordCount = 0

    // Process chapters (limit to first chapter if preview mode)
    const itemsToProcess = previewOnly ? spineItems.slice(0, 1) : spineItems

    for (let i = 0; i < itemsToProcess.length; i++) {
      const item = itemsToProcess[i]
      
      try {
        console.log(`Processing chapter ${i + 1}: ${item.id}`)
        
        // Get chapter content
        const chapterBuffer = await epub.getChapter(item.id)
        let content = chapterBuffer.toString('utf-8')

        // Clean up HTML content
        content = cleanHTMLContent(content)

        if (content.trim().length < 100) {
          console.log(`Skipping short chapter: ${content.length} characters`)
          continue
        }

        // Extract title from content or use default
        let title = extractTitleFromContent(content) || item.title || `Chapter ${chapters.length + 1}`

        // Count words
        const words = wordTokenizer.tokenize(content.toLowerCase()) || []
        const wordCount = words.length

        totalWordCount += wordCount

        chapters.push({
          title,
          content,
          chapter_number: chapters.length + 1,
          word_count: wordCount
        })

        console.log(`Chapter "${title}": ${wordCount} words`)

      } catch (chapterError) {
        console.error(`Error processing chapter ${i + 1}:`, chapterError)
        continue
      }
    }

    if (chapters.length === 0) {
      throw new Error('No valid chapters found in EPUB')
    }

    // Calculate reading time and page count
    const readingTimeResult = readingTime(chapters.map(ch => ch.content).join(' '))
    const pageCount = Math.ceil(totalWordCount / 250) // ~250 words per page

    console.log(`Processing complete: ${chapters.length} chapters, ${totalWordCount} words, ${readingTimeResult.minutes} min read`)

    return {
      chapters,
      wordCount: totalWordCount,
      readingTimeMinutes: readingTimeResult.minutes,
      pageCount,
      metadata
    }

  } catch (error) {
    console.error('EPUB processing error:', error)
    throw new Error(`EPUB processing failed: ${error.message}`)
  }
}

/**
 * Clean HTML content for display
 */
function cleanHTMLContent(html: string): string {
  // Remove DOCTYPE, html, head, body tags but keep content structure
  let cleaned = html
    .replace(/<!DOCTYPE[^>]*>/gi, '')
    .replace(/<\?xml[^>]*>/gi, '')
    .replace(/<html[^>]*>/gi, '')
    .replace(/<\/html>/gi, '')
    .replace(/<head[^>]*>[\s\S]*?<\/head>/gi, '')
    .replace(/<body[^>]*>/gi, '')
    .replace(/<\/body>/gi, '')

  // Clean up whitespace
  cleaned = cleaned
    .replace(/\s+/g, ' ')
    .replace(/>\s+</g, '><')
    .trim()

  return cleaned
}

/**
 * Extract title from chapter content
 */
function extractTitleFromContent(content: string): string | null {
  // Try to find h1, h2, or first strong/b tag
  const titlePatterns = [
    /<h[1-2][^>]*>(.*?)<\/h[1-2]>/i,
    /<strong[^>]*>(.*?)<\/strong>/i,
    /<b[^>]*>(.*?)<\/b>/i
  ]

  for (const pattern of titlePatterns) {
    const match = content.match(pattern)
    if (match && match[1]) {
      let title = match[1]
        .replace(/<[^>]*>/g, '') // Remove any nested HTML
        .trim()
      
      if (title.length > 0 && title.length <= 100) {
        return title
      }
    }
  }

  return null
}

/**
 * Check if content is likely a table of contents
 */
function isTableOfContents(content: string): boolean {
  const tocIndicators = [
    /table\s+of\s+contents/i,
    /contents/i,
    /chapter\s+\d+.*chapter\s+\d+/i, // Multiple "Chapter X" entries
    /<a[^>]*href[^>]*>.*chapter/i // Links to chapters
  ]

  const cleanContent = content.replace(/<[^>]*>/g, ' ').toLowerCase()
  
  return tocIndicators.some(pattern => pattern.test(cleanContent)) &&
         cleanContent.length < 2000 // TOCs are usually short
}

/**
 * Process PDF files - extract text and count words accurately
 */
async function processPDF(buffer: Buffer, previewOnly: boolean = false): Promise<ProcessingResult> {
  try {
    console.log('Processing PDF file - extracting text for accurate word count')

    // Import PDF.js for text extraction
    const pdfjsLib = await import('pdfjs-dist')

    // Set worker source
    pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js'

    // Load the PDF
    const pdf = await pdfjsLib.getDocument({ data: buffer }).promise
    console.log(`PDF loaded: ${pdf.numPages} pages`)

    let totalText = ''
    const maxPages = previewOnly ? Math.min(5, pdf.numPages) : pdf.numPages

    // Extract text from all pages
    for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum)
        const textContent = await page.getTextContent()
        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ')
        totalText += pageText + ' '

        if (pageNum % 10 === 0) {
          console.log(`Processed ${pageNum}/${maxPages} pages`)
        }
      } catch (pageError) {
        console.warn(`Failed to extract text from page ${pageNum}:`, pageError)
      }
    }

    // Clean and count words
    const cleanText = totalText
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s]/g, ' ')
      .trim()

    const words = cleanText.split(/\s+/).filter(word => word.length > 0)
    const actualWordCount = words.length

    // Calculate reading time (average 200 words per minute)
    const readingTimeMinutes = Math.max(Math.ceil(actualWordCount / 200), 1)

    console.log(`PDF processing complete: ${actualWordCount} words, ${readingTimeMinutes} minutes reading time`)

    // Create chapter structure for PDF
    const chapters = [{
      id: 'pdf-content',
      title: 'PDF Content',
      content: previewOnly ? cleanText.substring(0, 1000) + '...' : 'This PDF is ready to read using the PDF reader.',
      chapter_number: 1,
      word_count: actualWordCount,
      reading_time_minutes: readingTimeMinutes
    }]

    return {
      chapters,
      totalWords: actualWordCount,
      totalChapters: 1,
      estimatedReadingTime: readingTimeMinutes,
      wordCount: actualWordCount, // Add this for API compatibility
      pageCount: pdf.numPages, // Add page count
      metadata: {}, // Empty metadata object for PDFs
      suggestedTags: [], // Empty tags for PDFs
      description: '', // Empty description for PDFs
      processingStats: {
        originalChapters: 1,
        processedChapters: 1,
        skippedChapters: 0,
        totalWords: actualWordCount,
        averageWordsPerChapter: actualWordCount
      }
    }
  } catch (error) {
    console.error('Error processing PDF:', error)
    throw new Error(`PDF processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export { processEPUB, processPDF }
