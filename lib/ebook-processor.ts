import { createSupabaseServerClient } from '@/lib/supabase/client'

export interface Chapter {
  title: string
  content: string
  chapter_number: number
  word_count: number
}

export interface ProcessingResult {
  chapters: Chapter[]
  wordCount: number
  readingTimeMinutes: number
  pageCount: number
  metadata?: {
    title?: string
    author?: string
    description?: string
  }
}

/**
 * Main entry point for processing ebook files from URL
 */
export async function processEbook(projectId: string, fileUrl: string, fileType: 'pdf' | 'epub'): Promise<ProcessingResult> {
  try {
    console.log(`Processing ${fileType.toUpperCase()} from URL: ${fileUrl}`)

    if (fileType === 'pdf') {
      throw new Error('PDF files must be converted to EPUB format first using the chapter marking tool.')
    }

    // Download the file
    const response = await fetch(fileUrl)
    if (!response.ok) {
      throw new Error(`Failed to download file: ${response.statusText}`)
    }

    const buffer = await response.arrayBuffer()
    console.log('Buffer size:', buffer.byteLength)

    return await processEPUB(Buffer.from(buffer), false) // Full processing
  } catch (error) {
    console.error('Error processing ebook:', error)
    throw error
  }
}

/**
 * Process ebook buffer directly
 */
export async function processEbookBuffer(buffer: Buffer, fileType: 'pdf' | 'epub', previewOnly: boolean = false): Promise<ProcessingResult> {
  console.log(`Processing ${fileType.toUpperCase()} buffer${previewOnly ? ' (PREVIEW MODE - Chapter 1 only)' : ''}`)

  if (fileType === 'pdf') {
    return await processPDF(buffer, previewOnly)
  }

  return await processEPUB(buffer, previewOnly)
}

/**
 * Process EPUB files using epub2 library
 */
async function processEPUB(buffer: Buffer, previewOnly: boolean = false): Promise<ProcessingResult> {
  try {
    const { WordTokenizer } = await import('natural')
    const readingTime = (await import('reading-time')).default
    const EPub = (await import('epub2')).EPub

    const wordTokenizer = new WordTokenizer()

    console.log('Processing EPUB buffer, size:', buffer.length)

    // Parse EPUB
    const epub = await EPub.fromBuffer(buffer)
    console.log('EPUB parsed successfully')

    // Extract metadata
    const metadata = {
      title: epub.metadata.title || 'Untitled Book',
      author: epub.metadata.creator || 'Unknown Author',
      description: epub.metadata.description || ''
    }

    console.log('EPUB metadata:', metadata)

    // Get chapters from spine
    const spineItems = epub.flow
    console.log(`Found ${spineItems.length} spine items`)

    const chapters: Chapter[] = []
    let totalWordCount = 0

    // Process chapters (limit to first chapter if preview mode)
    const itemsToProcess = previewOnly ? spineItems.slice(0, 1) : spineItems

    for (let i = 0; i < itemsToProcess.length; i++) {
      const item = itemsToProcess[i]
      
      try {
        console.log(`Processing chapter ${i + 1}: ${item.id}`)
        
        // Get chapter content
        const chapterBuffer = await epub.getChapter(item.id)
        let content = chapterBuffer.toString('utf-8')

        // Clean up HTML content
        content = cleanHTMLContent(content)

        if (content.trim().length < 100) {
          console.log(`Skipping short chapter: ${content.length} characters`)
          continue
        }

        // Extract title from content or use default
        let title = extractTitleFromContent(content) || item.title || `Chapter ${chapters.length + 1}`

        // Count words
        const words = wordTokenizer.tokenize(content.toLowerCase()) || []
        const wordCount = words.length

        totalWordCount += wordCount

        chapters.push({
          title,
          content,
          chapter_number: chapters.length + 1,
          word_count: wordCount
        })

        console.log(`Chapter "${title}": ${wordCount} words`)

      } catch (chapterError) {
        console.error(`Error processing chapter ${i + 1}:`, chapterError)
        continue
      }
    }

    if (chapters.length === 0) {
      throw new Error('No valid chapters found in EPUB')
    }

    // Calculate reading time and page count
    const readingTimeResult = readingTime(chapters.map(ch => ch.content).join(' '))
    const pageCount = Math.ceil(totalWordCount / 250) // ~250 words per page

    console.log(`Processing complete: ${chapters.length} chapters, ${totalWordCount} words, ${readingTimeResult.minutes} min read`)

    return {
      chapters,
      wordCount: totalWordCount,
      readingTimeMinutes: readingTimeResult.minutes,
      pageCount,
      metadata
    }

  } catch (error) {
    console.error('EPUB processing error:', error)
    throw new Error(`EPUB processing failed: ${error.message}`)
  }
}

/**
 * Clean HTML content for display
 */
function cleanHTMLContent(html: string): string {
  // Remove DOCTYPE, html, head, body tags but keep content structure
  let cleaned = html
    .replace(/<!DOCTYPE[^>]*>/gi, '')
    .replace(/<\?xml[^>]*>/gi, '')
    .replace(/<html[^>]*>/gi, '')
    .replace(/<\/html>/gi, '')
    .replace(/<head[^>]*>[\s\S]*?<\/head>/gi, '')
    .replace(/<body[^>]*>/gi, '')
    .replace(/<\/body>/gi, '')

  // Clean up whitespace
  cleaned = cleaned
    .replace(/\s+/g, ' ')
    .replace(/>\s+</g, '><')
    .trim()

  return cleaned
}

/**
 * Extract title from chapter content
 */
function extractTitleFromContent(content: string): string | null {
  // Try to find h1, h2, or first strong/b tag
  const titlePatterns = [
    /<h[1-2][^>]*>(.*?)<\/h[1-2]>/i,
    /<strong[^>]*>(.*?)<\/strong>/i,
    /<b[^>]*>(.*?)<\/b>/i
  ]

  for (const pattern of titlePatterns) {
    const match = content.match(pattern)
    if (match && match[1]) {
      let title = match[1]
        .replace(/<[^>]*>/g, '') // Remove any nested HTML
        .trim()
      
      if (title.length > 0 && title.length <= 100) {
        return title
      }
    }
  }

  return null
}

/**
 * Check if content is likely a table of contents
 */
function isTableOfContents(content: string): boolean {
  const tocIndicators = [
    /table\s+of\s+contents/i,
    /contents/i,
    /chapter\s+\d+.*chapter\s+\d+/i, // Multiple "Chapter X" entries
    /<a[^>]*href[^>]*>.*chapter/i // Links to chapters
  ]

  const cleanContent = content.replace(/<[^>]*>/g, ' ').toLowerCase()
  
  return tocIndicators.some(pattern => pattern.test(cleanContent)) &&
         cleanContent.length < 2000 // TOCs are usually short
}

/**
 * Process PDF files - extract text and count words accurately
 */
async function processPDF(buffer: Buffer, previewOnly: boolean = false): Promise<ProcessingResult> {
  try {
    console.log('Processing PDF file - extracting text for accurate word count')

    // Use a more reliable approach - fetch the PDF from URL and process server-side
    const pdfjsLib = await import('pdfjs-dist/legacy/build/pdf.js')

    const pdf = await pdfjsLib.getDocument({
      data: new Uint8Array(buffer),
      useSystemFonts: true,
      verbosity: 0 // Reduce logging
    }).promise

    console.log(`PDF loaded: ${pdf.numPages} pages`)

    let allText = ''
    const maxPages = previewOnly ? Math.min(3, pdf.numPages) : pdf.numPages

    // Process pages in smaller batches to avoid memory issues
    for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum)
        const textContent = await page.getTextContent()

        // More robust text extraction
        const pageTexts = textContent.items
          .map((item: any) => {
            if (item && typeof item.str === 'string') {
              return item.str.trim()
            }
            return ''
          })
          .filter(text => text.length > 0)

        if (pageTexts.length > 0) {
          allText += pageTexts.join(' ') + ' '
        }

        // Clean up page to free memory
        page.cleanup()

      } catch (pageError) {
        console.warn(`Failed to process page ${pageNum}:`, pageError)
      }
    }

    // More aggressive text cleaning
    let cleanText = allText
      .replace(/\s+/g, ' ') // Multiple spaces to single space
      .replace(/[^\w\s\-']/g, ' ') // Keep only words, spaces, hyphens, apostrophes
      .replace(/\b\w{1}\b/g, ' ') // Remove single characters
      .replace(/\s+/g, ' ') // Clean up spaces again
      .trim()

    // Split into words and filter
    const words = cleanText
      .split(/\s+/)
      .filter(word => word.length > 1) // Only count words with 2+ characters
      .filter(word => /[a-zA-Z]/.test(word)) // Must contain at least one letter

    const wordCount = words.length
    const readingTime = Math.max(Math.ceil(wordCount / 200), 1)

    console.log(`PDF processing complete:`)
    console.log(`- Pages processed: ${maxPages}/${pdf.numPages}`)
    console.log(`- Word count: ${wordCount}`)
    console.log(`- Reading time: ${readingTime} minutes`)
    console.log(`- Text sample: "${cleanText.substring(0, 100)}..."`)

    if (wordCount < 50) {
      console.warn('Low word count - PDF might be image-based or have extraction issues')
    }

    const chapters = [{
      id: 'pdf-content',
      title: 'PDF Content',
      content: 'This PDF is ready to read using the PDF reader.',
      chapter_number: 1,
      word_count: wordCount,
      reading_time_minutes: readingTime
    }]

    return {
      chapters,
      totalWords: wordCount,
      totalChapters: 1,
      estimatedReadingTime: readingTime,
      wordCount: wordCount,
      pageCount: pdf.numPages,
      metadata: {},
      suggestedTags: [],
      description: '',
      processingStats: {
        originalChapters: 1,
        processedChapters: 1,
        skippedChapters: 0,
        totalWords: wordCount,
        averageWordsPerChapter: wordCount
      }
    }
  } catch (error) {
    console.error('PDF processing failed:', error)

    // Fallback to file size estimation if text extraction fails
    const fileSizeMB = buffer.length / (1024 * 1024)
    const fallbackWords = Math.max(Math.floor(fileSizeMB * 5000), 1000)
    const fallbackTime = Math.max(Math.ceil(fallbackWords / 200), 5)

    console.log(`Using fallback estimation: ${fallbackWords} words`)

    return {
      chapters: [{
        id: 'pdf-content',
        title: 'PDF Content',
        content: 'This PDF is ready to read using the PDF reader.',
        chapter_number: 1,
        word_count: fallbackWords,
        reading_time_minutes: fallbackTime
      }],
      totalWords: fallbackWords,
      totalChapters: 1,
      estimatedReadingTime: fallbackTime,
      wordCount: fallbackWords,
      pageCount: 1,
      metadata: {},
      suggestedTags: [],
      description: '',
      processingStats: {
        originalChapters: 1,
        processedChapters: 1,
        skippedChapters: 0,
        totalWords: fallbackWords,
        averageWordsPerChapter: fallbackWords
      }
    }
  }
}

export { processEPUB, processPDF }
