import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { projectId } = await request.json()
    
    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 })
    }

    const supabase = createSupabaseClient()
    
    // Get the project and PDF URL
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('ebook_file_url, ebook_file_type')
      .eq('id', projectId)
      .single()

    if (projectError || !project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    if (project.ebook_file_type !== 'pdf') {
      return NextResponse.json({ error: 'Not a PDF file' }, { status: 400 })
    }

    // Fetch the PDF file
    const response = await fetch(project.ebook_file_url)
    if (!response.ok) {
      throw new Error('Failed to fetch PDF file')
    }

    const arrayBuffer = await response.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    // Import PDF.js for server-side processing
    const pdfjsLib = await import('pdfjs-dist/legacy/build/pdf.js')
    
    // Load the PDF
    const pdf = await pdfjsLib.getDocument({ 
      data: new Uint8Array(buffer),
      useSystemFonts: true
    }).promise

    console.log(`Processing PDF: ${pdf.numPages} pages`)

    let totalText = ''
    
    // Extract text from all pages
    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum)
        const textContent = await page.getTextContent()
        
        const pageText = textContent.items
          .filter((item: any) => item.str && typeof item.str === 'string')
          .map((item: any) => item.str.trim())
          .filter((text: string) => text.length > 0)
          .join(' ')
        
        totalText += pageText + ' '
        
        if (pageNum % 10 === 0) {
          console.log(`Processed ${pageNum}/${pdf.numPages} pages`)
        }
      } catch (pageError) {
        console.warn(`Failed to extract text from page ${pageNum}:`, pageError)
      }
    }

    // Clean the text
    const cleanText = totalText
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s'-]/g, ' ')
      .trim()

    // Count words
    const words = cleanText.split(/\s+/).filter(word => word.length > 0)
    const wordCount = words.length
    const readingTime = Math.max(Math.ceil(wordCount / 200), 1)

    console.log(`Word count: ${wordCount}, Reading time: ${readingTime} minutes`)

    // Update the project with accurate word count
    const { error: updateError } = await supabase
      .from('projects')
      .update({
        total_words: wordCount,
        reading_time_minutes: readingTime,
        page_count: pdf.numPages
      })
      .eq('id', projectId)

    if (updateError) {
      console.error('Failed to update project:', updateError)
      return NextResponse.json({ error: 'Failed to update project' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      wordCount,
      readingTime,
      pageCount: pdf.numPages,
      textPreview: cleanText.substring(0, 200)
    })

  } catch (error) {
    console.error('Error counting PDF words:', error)
    return NextResponse.json({ 
      error: 'Failed to count words',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
