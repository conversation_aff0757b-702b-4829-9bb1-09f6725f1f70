import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseClient } from '@/lib/supabase/server'
import { exec } from 'child_process'
import { promisify } from 'util'
import fs from 'fs'
import path from 'path'

const execAsync = promisify(exec)

export async function POST(request: NextRequest) {
  try {
    const { projectId } = await request.json()

    if (!projectId) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 })
    }

    const supabase = createSupabaseClient()

    // Get the project and PDF URL
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('ebook_file_url, ebook_file_type')
      .eq('id', projectId)
      .single()

    if (projectError || !project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    if (project.ebook_file_type !== 'pdf') {
      return NextResponse.json({ error: 'Not a PDF file' }, { status: 400 })
    }

    // Download the PDF file
    const response = await fetch(project.ebook_file_url)
    if (!response.ok) {
      throw new Error('Failed to fetch PDF file')
    }

    const arrayBuffer = await response.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    // Save PDF to temporary file
    const tempDir = '/tmp'
    const tempFile = path.join(tempDir, `pdf_${projectId}.pdf`)
    fs.writeFileSync(tempFile, buffer)

    try {
      // Run Python script to count words
      const scriptPath = path.join(process.cwd(), 'scripts', 'count_pdf_words.py')
      const { stdout } = await execAsync(`python3 ${scriptPath} ${tempFile}`)

      const result = JSON.parse(stdout.trim())

      if (!result.success) {
        throw new Error(result.error)
      }

      console.log(`Python word count result: ${result.word_count} words`)

      // Update the project with accurate word count
      const { error: updateError } = await supabase
        .from('projects')
        .update({
          total_words: result.word_count,
          reading_time_minutes: result.reading_time_minutes,
          page_count: 1 // We'll get page count separately if needed
        })
        .eq('id', projectId)

      if (updateError) {
        console.error('Failed to update project:', updateError)
        return NextResponse.json({ error: 'Failed to update project' }, { status: 500 })
      }

      return NextResponse.json({
        success: true,
        wordCount: result.word_count,
        readingTime: result.reading_time_minutes,
        textPreview: result.text_preview
      })

    } finally {
      // Clean up temp file
      if (fs.existsSync(tempFile)) {
        fs.unlinkSync(tempFile)
      }
    }

  } catch (error) {
    console.error('Error counting PDF words:', error)
    return NextResponse.json({
      error: 'Failed to count words',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
