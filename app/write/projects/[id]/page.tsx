'use client'

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { projectCoverStorage } from "@/lib/supabase/storage"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { PdfChapterMarker } from "@/components/PdfChapterMarker"

interface Project {
  id: string
  title: string
  description: string
  cover_image_url: string
  genre: string
  is_private: boolean
  is_complete: boolean
  is_ebook: boolean
  ebook_file_url?: string
  price_type: 'project' | 'chapters'
  price_amount: number
  total_chapters: number
  total_words: number
  created_at: string
  updated_at: string
}

interface Chapter {
  id: string
  title: string
  chapter_number: number
  word_count: number
  is_published: boolean
  love_count: number
  created_at: string
  updated_at: string
}

export default function ProjectPage({ params }: { params: Promise<{ id: string }> }) {
  const [project, setProject] = useState<Project | null>(null)
  const [chapters, setChapters] = useState<Chapter[]>([])
  const [user, setUser] = useState<unknown>(null)
  const [loading, setLoading] = useState(true)
  const [creatingChapter, setCreatingChapter] = useState(false)
  const [showPricingModal, setShowPricingModal] = useState(false)
  const [showEbookModal, setShowEbookModal] = useState(false)
  const [showCoverModal, setShowCoverModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showFileUploadModal, setShowFileUploadModal] = useState(false)
  const [, setUploadingCover] = useState(false)
  const [deleting, setDeleting] = useState(false)
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    const initializePage = async () => {
      const resolvedParams = await params
      const projectId = resolvedParams.id

      const { data: { user: authUser }, error } = await supabase.auth.getUser()
      
      if (error || !authUser) {
        router.push('/login')
        return
      }

      // Get user profile
      const { data: profile } = await supabase
        .from("users")
        .select("*")
        .eq("id", authUser.id)
        .single()

      if (!profile || (profile.role !== 'user' && profile.role !== 'admin')) {
        router.push('/')
        return
      }

      setUser(profile)

      // Load project
      const { data: projectData, error: projectError } = await supabase
        .from("projects")
        .select("*")
        .eq("id", projectId)
        .eq("user_id", authUser.id)
        .single()

      if (projectError || !projectData) {
        router.push('/write/projects')
        return
      }

      setProject(projectData)

      // Load chapters
      const { data: chaptersData } = await supabase
        .from("chapters")
        .select("*")
        .eq("project_id", projectId)
        .order("chapter_number", { ascending: true })

      setChapters(chaptersData || [])
      setLoading(false)
    }

    initializePage()
  }, [params, router, supabase])

  // Calculate dynamic word count from chapters
  const dynamicWordCount = chapters.reduce((total, chapter) => total + chapter.word_count, 0)

  const fetchProject = async () => {
    if (!user) return

    const resolvedParams = await params
    const projectId = resolvedParams.id

    const { data: projectData, error: projectError } = await supabase
      .from("projects")
      .select("*")
      .eq("id", projectId)
      .eq("user_id", user.id)
      .single()

    if (projectError || !projectData) {
      router.push('/write/projects')
      return
    }

    setProject(projectData)
  }

  const createNewChapter = async () => {
    if (!project || !user) return

    setCreatingChapter(true)
    const nextChapterNumber = chapters.length + 1

    try {
      const { data: newChapter, error } = await supabase
        .from("chapters")
        .insert({
          project_id: project.id,
          user_id: user.id,
          title: `Chapter ${nextChapterNumber}`,
          content: "",
          chapter_number: nextChapterNumber,
        })
        .select()
        .single()

      if (error) throw error

      router.push(`/write/projects/${project.id}/chapters/${newChapter.id}`)
    } catch (error) {
      console.error('Error creating chapter:', error)
      setCreatingChapter(false)
    }
  }

  const deleteProject = async () => {
    if (!project || !user) return

    setDeleting(true)
    try {
      // Delete related data first
      await supabase.from('chapters').delete().eq('project_id', project.id)
      await supabase.from('book_purchases').delete().eq('book_id', project.id)
      await supabase.from('book_reviews').delete().eq('book_id', project.id)

      // Delete the project itself
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', project.id)
        .eq('user_id', user.id) // Extra safety check

      if (error) throw error

      // Redirect to projects list
      router.push('/write/projects')
    } catch (error) {
      console.error('Error deleting project:', error)
      setDeleting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-gray-600 font-serif">Loading...</div>
      </div>
    )
  }

  if (!project || !user) return null

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 py-8">
        
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-start gap-8 mb-8">
          
          {/* Cover & Basic Info */}
          <div className="lg:w-80 flex-shrink-0">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-100/50">
              
              {/* Cover Image - KDP Standard 10:16 ratio */}
              <div
                onClick={() => setShowCoverModal(true)}
                className="aspect-[10/16] bg-gradient-to-br from-purple-100 to-blue-100 rounded-xl overflow-hidden mb-4 cursor-pointer group relative"
              >
                {project.cover_image_url ? (
                  <img
                    src={project.cover_image_url}
                    alt={project.title}
                    className="w-full h-full object-cover group-hover:opacity-75 transition-opacity"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center group-hover:bg-purple-200 transition-colors">
                    <span className="text-6xl opacity-50">📖</span>
                  </div>
                )}

                {/* Hover overlay */}
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <div className="text-white text-center">
                    <div className="text-2xl mb-2">📷</div>
                    <div className="text-sm font-medium">
                      {project.cover_image_url ? 'Change Cover' : 'Add Cover'}
                    </div>
                  </div>
                </div>
              </div>

              <p className="text-xs text-gray-500 text-center mb-4">
                Click to {project.cover_image_url ? 'change' : 'add'} cover
              </p>

              {/* Project Stats */}
              <div className="space-y-3 text-sm">
                {project?.is_ebook ? (
                  // For finished ebooks, show different stats
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Type:</span>
                      <span className="font-medium text-green-600">Complete Ebook</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Words:</span>
                      <span className="font-medium">{(project.total_words || 0).toLocaleString()}</span>
                    </div>
                  </>
                ) : (
                  // For WIP books, show chapter-based stats
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Chapters:</span>
                      <span className="font-medium">{chapters.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Words:</span>
                      <span className="font-medium">{dynamicWordCount.toLocaleString()}</span>
                    </div>
                  </>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-500">Status:</span>
                  <span className={`font-medium ${project.is_complete ? 'text-green-600' : 'text-blue-600'}`}>
                    {project.is_complete ? 'Complete' : 'In Progress'}
                  </span>
                </div>
                {project.genre && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Genre:</span>
                    <span className="font-medium">{project.genre}</span>
                  </div>
                )}
                {project.price_amount && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Price:</span>
                    <span className="font-medium text-purple-600">
                      ${(project.price_amount / 100).toFixed(2)}
                      {project.price_type === 'chapters' ? '/30 chapters' : ' full access'}
                    </span>
                  </div>
                )}
              </div>
              
              {/* Actions */}
              <div className="mt-6 space-y-3">
                {!project?.is_ebook && (
                  <Button
                    onClick={createNewChapter}
                    isLoading={creatingChapter}
                    disabled={creatingChapter}
                    className="w-full bg-purple-600 text-white hover:bg-purple-700"
                  >
                    {creatingChapter ? 'Creating Chapter...' : '+ New Chapter'}
                  </Button>
                )}

                <Button
                  onClick={() => setShowPricingModal(true)}
                  variant="secondary"
                  className="w-full"
                >
                  Set Pricing
                </Button>

                {!project?.is_ebook && (
                  <Button
                    onClick={() => setShowEbookModal(true)}
                    variant="outline"
                    className="w-full"
                  >
                    📚 Convert to Ebook
                  </Button>
                )}

                {project?.is_ebook && (
                  <>
                    <Button
                      onClick={() => setShowFileUploadModal(true)}
                      variant="outline"
                      className="w-full"
                    >
                      📄 Upload New File
                    </Button>

                    {project.ebook_file_type === 'pdf' && (
                      <Button
                        onClick={async () => {
                          try {
                            const response = await fetch('/api/count-pdf-words', {
                              method: 'POST',
                              headers: { 'Content-Type': 'application/json' },
                              body: JSON.stringify({ projectId: project.id })
                            })
                            const data = await response.json()
                            if (data.success) {
                              alert(`Word count updated: ${data.wordCount.toLocaleString()} words`)
                              window.location.reload()
                            } else {
                              alert(`Error: ${data.error}`)
                            }
                          } catch (error) {
                            alert('Failed to count words')
                          }
                        }}
                        variant="outline"
                        className="w-full"
                      >
                        🔢 Recount Words
                      </Button>
                    )}
                  </>
                )}

                {/* Delete Button */}
                <Button
                  onClick={() => setShowDeleteModal(true)}
                  variant="outline"
                  className="w-full text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300"
                >
                  🗑️ Delete {project?.is_ebook ? 'Book' : 'Project'}
                </Button>
              </div>
            </div>
          </div>
          
          {/* Main Content */}
          <div className="flex-1">
            {/* Navigation */}
            <div className="mb-4">
              <Link
                href="/write/projects"
                className="text-gray-600 hover:text-gray-800 font-medium text-sm transition-colors inline-flex items-center gap-1"
              >
                ← All Projects
              </Link>
            </div>

            {/* Title and Description */}
            <div className="mb-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h1 className="text-2xl sm:text-3xl font-serif text-gray-800 mb-2">
                    {project.title}
                  </h1>
                  {project.description && (
                    <p className="text-gray-600 font-serif leading-relaxed">
                      {project.description}
                    </p>
                  )}
                </div>
                <Button
                  onClick={fetchProject}
                  variant="outline"
                  size="sm"
                  className="ml-4 flex-shrink-0"
                >
                  🔄 Refresh
                </Button>
              </div>
            </div>
            
            {/* Chapters List */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-100/50">
              <div className="p-6 border-b border-gray-100">
                <h2 className="text-xl font-serif text-gray-800">Chapters</h2>
              </div>
              
              {chapters.length === 0 ? (
                <div className="p-12 text-center">
                  {project?.is_ebook ? (
                    // Finished ebook - no individual chapters to manage
                    <>
                      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-2xl">📚</span>
                      </div>
                      <h3 className="text-lg font-serif text-gray-800 mb-2">
                        Complete Ebook
                      </h3>
                      <p className="text-gray-600 font-serif mb-6">
                        This is a finished book uploaded as a complete document. Readers will access the full content through the e-reader.
                      </p>
                      <Button
                        onClick={() => window.open(`/books/${project.id}`, '_blank')}
                        className="bg-purple-600 text-white hover:bg-purple-700"
                      >
                        📚 View Book Details
                      </Button>
                    </>
                  ) : (
                    // Work in progress - can add chapters
                    <>
                      <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-2xl">📝</span>
                      </div>
                      <h3 className="text-lg font-serif text-gray-800 mb-2">
                        No chapters yet
                      </h3>
                      <p className="text-gray-600 font-serif mb-6">
                        Start writing your first chapter to begin your project.
                      </p>
                      <Button
                        onClick={createNewChapter}
                        isLoading={creatingChapter}
                        disabled={creatingChapter}
                        className="bg-purple-600 text-white hover:bg-purple-700"
                      >
                        {creatingChapter ? 'Creating Chapter...' : 'Write First Chapter'}
                      </Button>
                    </>
                  )}
                </div>
              ) : (
                <div className="divide-y divide-gray-100">
                  {chapters.map((chapter) => (
                    <Link
                      key={chapter.id}
                      href={`/write/projects/${project.id}/chapters/${chapter.id}`}
                      className="block p-6 hover:bg-gray-50/50 transition-colors group"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h3 className="font-serif text-lg text-gray-800 group-hover:text-purple-600 transition-colors">
                            {chapter.title}
                          </h3>
                          <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                            <span>Chapter {chapter.chapter_number}</span>
                            <span>•</span>
                            <span>{chapter.word_count.toLocaleString()} words</span>
                            <span>•</span>
                            <span className={chapter.is_published ? 'text-green-600' : 'text-blue-600'}>
                              {chapter.is_published ? 'Published' : 'Draft'}
                            </span>
                          </div>
                        </div>
                        
                        <div className="text-right text-sm text-gray-500">
                          <div>Updated</div>
                          <div>{new Date(chapter.updated_at).toLocaleDateString()}</div>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Pricing Modal */}
        {showPricingModal && (
          <PricingModal
            project={project}
            onClose={() => setShowPricingModal(false)}
            onSuccess={(updatedProject) => {
              setProject(updatedProject)
              setShowPricingModal(false)
            }}
          />
        )}

        {/* Cover Upload Modal */}
        {showCoverModal && (
          <CoverUploadModal
            project={project}
            onClose={() => setShowCoverModal(false)}
            onSuccess={(updatedProject) => {
              setProject(updatedProject)
              setShowCoverModal(false)
            }}
          />
        )}

        {/* Ebook Conversion Modal */}
        {showEbookModal && (
          <EbookConversionModal
            project={project}
            onClose={() => setShowEbookModal(false)}
            onUpdate={fetchProject}
          />
        )}

        {/* File Upload Modal */}
        {showFileUploadModal && (
          <FileUploadModal
            project={project}
            onClose={() => setShowFileUploadModal(false)}
            onUpdate={fetchProject}
          />
        )}

        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl p-6 w-full max-w-md">
              <h3 className="text-lg font-medium text-gray-900 mb-2">Delete {project?.is_ebook ? 'Book' : 'Project'}</h3>
              <p className="text-gray-600 mb-4">
                Are you sure you want to delete "{project.title}"? This action cannot be undone and will permanently remove all content.
              </p>
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteModal(false)}
                  className="flex-1"
                  disabled={deleting}
                >
                  Cancel
                </Button>
                <Button
                  onClick={deleteProject}
                  disabled={deleting}
                  className="flex-1 bg-red-600 text-white hover:bg-red-700"
                >
                  {deleting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Deleting...
                    </>
                  ) : (
                    'Delete Forever'
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Pricing Modal Component
function PricingModal({
  project,
  onClose,
  onSuccess
}: {
  project: Project
  onClose: () => void
  onSuccess: (project: Project) => void
}) {
  const [priceType, setPriceType] = useState<'project' | 'chapters'>(project.price_type || 'project')
  const [priceAmount, setPriceAmount] = useState(project.price_amount ? (project.price_amount / 100).toString() : '')
  const [loading, setLoading] = useState(false)
  const supabase = createSupabaseClient()

  const handleSave = async () => {
    setLoading(true)
    try {
      const amountInCents = priceAmount ? Math.round(parseFloat(priceAmount) * 100) : null

      const { data: updatedProject, error } = await supabase
        .from("projects")
        .update({
          price_type: priceType,
          price_amount: amountInCents,
        })
        .eq("id", project.id)
        .select()
        .single()

      if (error) throw error

      onSuccess(updatedProject)
    } catch (error) {
      console.error('Error updating pricing:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl p-6 w-full max-w-md">
        <h2 className="text-xl font-serif text-gray-800 mb-4">Set Project Pricing</h2>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Pricing Model
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="project"
                  checked={priceType === 'project'}
                  onChange={(e) => setPriceType(e.target.value as 'project')}
                  className="mr-2"
                />
                <span>Full Project Access - Buyer gets unlimited access to entire project</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="chapters"
                  checked={priceType === 'chapters'}
                  onChange={(e) => setPriceType(e.target.value as 'chapters')}
                  className="mr-2"
                />
                <span>Per Chapter Access - Buyer pays per 30 chapters</span>
              </label>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Price {priceType === 'chapters' ? '(per 30 chapters)' : '(full access)'}
            </label>
            <div className="relative">
              <span className="absolute left-3 top-2 text-gray-500">$</span>
              <input
                type="number"
                min="0"
                step="0.01"
                value={priceAmount}
                onChange={(e) => setPriceAmount(e.target.value)}
                className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="0.00"
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Leave empty for free access. {priceType === 'project' ? 'One-time payment for full access.' : 'Recurring payment per 30 chapters.'}
            </p>
          </div>
        </div>

        <div className="flex gap-3 mt-6">
          <Button
            onClick={onClose}
            variant="secondary"
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            isLoading={loading}
            className="flex-1 bg-purple-600 text-white hover:bg-purple-700"
          >
            Save Pricing
          </Button>
        </div>
      </div>
    </div>
  )
}

// Cover Upload Modal Component
function CoverUploadModal({
  project,
  onClose,
  onSuccess
}: {
  project: Project
  onClose: () => void
  onSuccess: (project: Project) => void
}) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [uploading, setUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const supabase = createSupabaseClient()

  const handleFileSelect = (file: File) => {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file')
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB')
      return
    }

    setSelectedFile(file)

    // Create preview URL
    const url = URL.createObjectURL(file)
    setPreviewUrl(url)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleUpload = async () => {
    if (!selectedFile) return

    setUploading(true)
    try {
      // Create unique filename
      const fileExt = selectedFile.name.split('.').pop()
      const fileName = `${project.id}-cover-${Date.now()}.${fileExt}`

      console.log('Uploading file:', fileName, 'Size:', selectedFile.size)

      // Upload to Supabase Storage with enhanced retry logic
      const { data: uploadData, error: uploadError } = await projectCoverStorage.upload(
        fileName,
        selectedFile,
        {
          cacheControl: '3600',
          upsert: false
        }
      )

      if (uploadError) {
        console.error('Upload error details:', uploadError)
        throw new Error(`Upload failed: ${uploadError.message}`)
      }

      console.log('Upload successful:', uploadData)

      // Get public URL
      const { data: { publicUrl } } = projectCoverStorage.getPublicUrl(fileName)

      console.log('Public URL:', publicUrl)

      // Update project with new cover URL
      const { data: updatedProject, error: updateError } = await supabase
        .from("projects")
        .update({ cover_image_url: publicUrl })
        .eq("id", project.id)
        .select()
        .single()

      if (updateError) {
        console.error('Database update error:', updateError)
        throw new Error(`Database update failed: ${updateError.message}`)
      }

      console.log('Project updated successfully')
      onSuccess(updatedProject)
    } catch (error) {
      console.error('Error uploading cover:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      alert(`Failed to upload cover: ${errorMessage}`)
    } finally {
      setUploading(false)
    }
  }

  const removeCover = async () => {
    setUploading(true)
    try {
      const { data: updatedProject, error } = await supabase
        .from("projects")
        .update({ cover_image_url: null })
        .eq("id", project.id)
        .select()
        .single()

      if (error) throw error

      onSuccess(updatedProject)
    } catch (error) {
      console.error('Error removing cover:', error)
      alert('Failed to remove cover. Please try again.')
    } finally {
      setUploading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl p-6 w-full max-w-md">
        <h2 className="text-xl font-serif text-gray-800 mb-4">Book Cover</h2>

        {/* Current Cover Preview */}
        {(previewUrl || project.cover_image_url) && (
          <div className="mb-4">
            <div className="aspect-[10/16] bg-gray-100 rounded-lg overflow-hidden max-w-32 mx-auto">
              <img
                src={previewUrl || project.cover_image_url || ''}
                alt="Cover preview"
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        )}

        {/* File Upload Area */}
        <div
          onDrop={handleDrop}
          onDragOver={(e) => { e.preventDefault(); setDragActive(true) }}
          onDragLeave={() => setDragActive(false)}
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            dragActive ? 'border-purple-400 bg-purple-50' : 'border-gray-300'
          }`}
        >
          <div className="text-4xl mb-2">📷</div>
          <p className="text-gray-600 mb-2">
            Drag & drop your book cover here
          </p>
          <p className="text-xs text-gray-500 mb-4">
            Recommended: 10:16 aspect ratio (KDP standard)<br/>
            Max file size: 5MB • JPG, PNG, WebP
          </p>

          <input
            type="file"
            accept="image/*"
            onChange={(e) => {
              const file = e.target.files?.[0]
              if (file) handleFileSelect(file)
            }}
            className="hidden"
            id="cover-upload"
          />
          <label
            htmlFor="cover-upload"
            className="bg-purple-600 text-white px-4 py-2 rounded-lg cursor-pointer hover:bg-purple-700 transition-colors inline-block"
          >
            Choose File
          </label>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 mt-6">
          <Button
            onClick={onClose}
            variant="secondary"
            className="flex-1"
            disabled={uploading}
          >
            Cancel
          </Button>

          {project.cover_image_url && !selectedFile && (
            <Button
              onClick={removeCover}
              isLoading={uploading}
              variant="secondary"
              className="flex-1 text-red-600 hover:text-red-700"
            >
              Remove Cover
            </Button>
          )}

          {selectedFile && (
            <Button
              onClick={handleUpload}
              isLoading={uploading}
              className="flex-1 bg-purple-600 text-white hover:bg-purple-700"
            >
              {uploading ? 'Uploading...' : 'Upload Cover'}
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

// Ebook Conversion Modal Component
function EbookConversionModal({
  project,
  onClose,
  onUpdate
}: {
  project: Project
  onClose: () => void
  onUpdate: () => void
}) {
  const [priceAmount, setPriceAmount] = useState('')
  const [genre, setGenre] = useState(project.genre || '')
  const [description, setDescription] = useState(project.description || '')
  const [previewChapters, setPreviewChapters] = useState('1')
  const [loading, setLoading] = useState(false)
  const supabase = createSupabaseClient()

  const handleConvert = async () => {
    setLoading(true)
    try {
      const amountInCents = priceAmount ? Math.round(parseFloat(priceAmount) * 100) : 0

      const { error } = await supabase
        .from("projects")
        .update({
          is_ebook: true,
          price_amount: amountInCents,
          genre: genre.trim() || null,
          description: description.trim() || null,
          preview_chapters: parseInt(previewChapters) || 1,
          is_complete: true // Mark as complete when converting to ebook
        })
        .eq("id", project.id)

      if (error) throw error

      onUpdate()
      onClose()
    } catch (error) {
      console.error('Error converting to ebook:', error)
      alert('Failed to convert to ebook. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl p-6 w-full max-w-lg max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-serif text-gray-800 mb-4">📚 Convert to Ebook</h2>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-start gap-3">
            <span className="text-blue-600 text-lg">ℹ️</span>
            <div>
              <h3 className="font-medium text-blue-900 mb-1">What happens when you convert?</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Your project becomes available in the book store</li>
                <li>• Readers can purchase and read your complete work</li>
                <li>• You can set pricing and preview chapters</li>
                <li>• Reviews will be enabled for paying customers</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Book Price
            </label>
            <div className="relative">
              <span className="absolute left-3 top-2 text-gray-500">$</span>
              <input
                type="number"
                min="0"
                step="0.01"
                value={priceAmount}
                onChange={(e) => setPriceAmount(e.target.value)}
                className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="0.00"
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Leave empty or set to 0 for free ebook
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Genre
            </label>
            <input
              type="text"
              value={genre}
              onChange={(e) => setGenre(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="Fiction, Romance, Sci-Fi, etc."
              maxLength={50}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Book Description
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 h-24 resize-none"
              placeholder="Write a compelling description for your book..."
              maxLength={500}
            />
            <p className="text-xs text-gray-500 mt-1">
              {description.length}/500 characters
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Preview Chapters
            </label>
            <select
              value={previewChapters}
              onChange={(e) => setPreviewChapters(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="1">1 chapter</option>
              <option value="2">2 chapters</option>
              <option value="3">3 chapters</option>
            </select>
            <p className="text-xs text-gray-500 mt-1">
              How many chapters readers can preview for free
            </p>
          </div>
        </div>

        <div className="flex gap-3 mt-6">
          <Button
            onClick={onClose}
            variant="secondary"
            className="flex-1"
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConvert}
            isLoading={loading}
            className="flex-1 bg-purple-600 text-white hover:bg-purple-700"
          >
            {loading ? 'Converting...' : 'Convert to Ebook'}
          </Button>
        </div>
      </div>
    </div>
  )
}

// File Upload Modal Component
function FileUploadModal({
  project,
  onClose,
  onUpdate
}: {
  project: Project
  onClose: () => void
  onUpdate: () => void
}) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [dragActive, setDragActive] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [previewChapters, setPreviewChapters] = useState<any[]>([])
  const [processingPreview, setProcessingPreview] = useState(false)
  const [showPdfMarker, setShowPdfMarker] = useState(false)
  const supabase = createSupabaseClient()

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    const files = e.dataTransfer.files
    if (files && files[0]) {
      const file = files[0]
      if (file.type === 'application/pdf' || file.type === 'application/epub+zip') {
        setSelectedFile(file)
      } else {
        alert('Please select a PDF or EPUB file')
      }
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files[0]) {
      const file = files[0]
      if (file.type === 'application/pdf' || file.type === 'application/epub+zip') {
        setSelectedFile(file)
      } else {
        alert('Please select a PDF or EPUB file')
      }
    }
  }

  const handlePreview = async () => {
    if (!selectedFile) return

    // For PDF files, show simple preview (word counting happens on server)
    if (selectedFile.type === 'application/pdf') {
      setProcessingPreview(true)
      try {
        const fileSizeMB = selectedFile.size / (1024 * 1024)
        const estimatedWords = Math.max(Math.floor(fileSizeMB * 8000), 2000)
        const estimatedReadingTime = Math.max(Math.floor(estimatedWords / 200), 10)

        const previewData = [{
          title: 'PDF Content',
          content: `This PDF will be processed to extract the exact word count. Estimated: ${estimatedWords.toLocaleString()} words. Click "Accept & Process" to upload and get accurate word counts from the actual text content.`,
          word_count: estimatedWords,
          reading_time_minutes: estimatedReadingTime
        }]

        setPreviewChapters(previewData)
        setShowPreview(true)
      } catch (error) {
        console.error('PDF preview error:', error)
        alert('Failed to generate PDF preview. Please try again.')
      } finally {
        setProcessingPreview(false)
      }
      return
    }

    // For EPUB files, use the existing preview
    setProcessingPreview(true)
    try {
      const formData = new FormData()
      formData.append('file', selectedFile)

      const response = await fetch('/api/preview-ebook', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error('Failed to process file for preview')
      }

      const result = await response.json()
      setPreviewChapters(result.chapters || [])
      setShowPreview(true)
    } catch (error) {
      console.error('Preview error:', error)
      alert('Failed to generate preview. Please try again.')
    } finally {
      setProcessingPreview(false)
    }
  }

  const handleUpload = async () => {
    if (!selectedFile) return

    setUploading(true)
    try {
      // Step 1: Upload file to Supabase storage
      const fileExt = selectedFile.name.split('.').pop()?.toLowerCase()
      const fileName = `${Date.now()}-${project.id}-update.${fileExt}`

      const { data: fileUpload, error: fileError } = await supabase.storage
        .from('ebooks')
        .upload(fileName, selectedFile, {
          cacheControl: '3600',
          upsert: false
        })

      if (fileError) {
        throw new Error(`File upload failed: ${fileError.message}`)
      }

      // Step 2: Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('ebooks')
        .getPublicUrl(fileName)

      // Step 3: Process the uploaded file (both EPUB and PDF need processing for accurate word counts)
      const response = await fetch('/api/process-ebook', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId: project.id,
          fileUrl: publicUrl,
          fileType: fileExt
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to process file')
      }

      alert('File uploaded and processed successfully!')
      onUpdate()
      onClose()
    } catch (error) {
      console.error('Upload error:', error)
      alert(`Failed to upload file: ${error.message}`)
    } finally {
      setUploading(false)
    }
  }

  const handlePdfConversion = async (chapters: any[]) => {
    if (!selectedFile) return

    setUploading(true)
    try {
      // Upload PDF file first
      const fileExt = selectedFile.name.split('.').pop()?.toLowerCase()
      const fileName = `${Date.now()}-${project.id}-update.${fileExt}`

      const { data: fileUpload, error: fileError } = await supabase.storage
        .from('ebooks')
        .upload(fileName, selectedFile, {
          cacheControl: '3600',
          upsert: false
        })

      if (fileError) {
        throw new Error(`File upload failed: ${fileError.message}`)
      }

      const { data: { publicUrl } } = supabase.storage
        .from('ebooks')
        .getPublicUrl(fileName)

      // Convert PDF to EPUB
      const response = await fetch('/api/convert-pdf-to-epub', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId: project.id,
          fileUrl: publicUrl,
          chapters
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to convert PDF')
      }

      alert('PDF successfully converted to EPUB format!')
      onUpdate()
      onClose()
    } catch (error) {
      console.error('PDF conversion error:', error)
      alert(`Failed to convert PDF: ${error.message}`)
    } finally {
      setUploading(false)
    }
  }

  if (showPdfMarker && selectedFile) {
    const pdfUrl = URL.createObjectURL(selectedFile)
    return (
      <PdfChapterMarker
        pdfUrl={pdfUrl}
        title={project.title}
        author={project.author_name || 'Unknown Author'}
        onClose={() => setShowPdfMarker(false)}
        onConvert={handlePdfConversion}
      />
    )
  }

  if (showPreview) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-serif text-gray-800">Preview: {selectedFile?.name}</h2>
            <button
              onClick={() => setShowPreview(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>

          <div className="space-y-4 mb-6">
            {previewChapters.slice(0, 3).map((chapter, index) => (
              <div key={index} className="border rounded-lg p-4">
                <h3 className="font-semibold mb-2">{chapter.title}</h3>
                <p className="text-sm text-gray-600 mb-2">{chapter.word_count} words</p>
                <div className="text-sm text-gray-800 line-clamp-3">
                  {chapter.content.substring(0, 200)}...
                </div>
              </div>
            ))}
            {previewChapters.length > 3 && (
              <p className="text-sm text-gray-600">
                ...and {previewChapters.length - 3} more chapters
              </p>
            )}
          </div>

          <div className="flex gap-3">
            <Button
              onClick={() => setShowPreview(false)}
              variant="outline"
              className="flex-1"
            >
              ← Back to Upload
            </Button>
            <Button
              onClick={handleUpload}
              isLoading={uploading}
              className="flex-1 bg-green-600 text-white hover:bg-green-700"
            >
              {uploading ? 'Processing...' : '✓ Accept & Process'}
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl p-6 w-full max-w-md">
        <h2 className="text-xl font-serif text-gray-800 mb-4">Upload New Ebook File</h2>

        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            dragActive
              ? 'border-purple-400 bg-purple-50'
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          {selectedFile ? (
            <div>
              <div className="text-4xl mb-2">📄</div>
              <p className="font-medium text-gray-900">{selectedFile.name}</p>
              <p className="text-sm text-gray-500">
                {(selectedFile.size / 1024 / 1024).toFixed(1)} MB
              </p>
              <button
                onClick={() => setSelectedFile(null)}
                className="text-sm text-red-600 hover:text-red-700 mt-2"
              >
                Remove file
              </button>
            </div>
          ) : (
            <div>
              <div className="text-4xl mb-2">📚</div>
              <p className="text-gray-600 mb-2">
                Drag and drop your PDF or EPUB file here
              </p>
              <p className="text-sm text-gray-500 mb-4">or</p>
              <label className="inline-block bg-purple-600 text-white px-4 py-2 rounded-lg cursor-pointer hover:bg-purple-700">
                Choose File
                <input
                  type="file"
                  accept=".pdf,.epub"
                  onChange={handleFileSelect}
                  className="hidden"
                />
              </label>
            </div>
          )}
        </div>

        <div className="flex gap-3 mt-6">
          <Button
            onClick={onClose}
            variant="outline"
            className="flex-1"
          >
            Cancel
          </Button>
          {selectedFile && (
            <Button
              onClick={handlePreview}
              isLoading={processingPreview}
              className="flex-1 bg-purple-600 text-white hover:bg-purple-700"
            >
              {processingPreview ? 'Processing...' : '👁️ Preview'}
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
