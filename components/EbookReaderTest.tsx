"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"

interface EbookReaderTestProps {
  bookTitle: string
  onClose: () => void
}

export function EbookReaderTest({ bookTitle, onClose }: EbookReaderTestProps) {
  return (
    <div className="fixed inset-0 z-50 bg-white">
      <div className="p-4">
        <h1>{bookTitle}</h1>
        <Button onClick={onClose}>Close</Button>
      </div>
    </div>
  )
}
