"use client"

import { useState, useEffect, useRef } from 'react'
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCw, Download, Share2 } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

interface PdfReaderContentProps {
  pdfUrl: string
  bookTitle: string
  fontSize: number
  fontFamily: string
  lineHeight: number
}

export function PdfReaderContent({ pdfUrl, bookTitle, fontSize, fontFamily, lineHeight }: PdfReaderContentProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [scale, setScale] = useState(1.0)
  const [rotation, setRotation] = useState(0)
  const [loading, setLoading] = useState(true)
  const [pdfDoc, setPdfDoc] = useState<any>(null)
  const [renderedPages, setRenderedPages] = useState<string[]>([])
  const [isPageTurning, setIsPageTurning] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const viewerRef = useRef<HTMLDivElement>(null)

  // Calculate reading progress
  const readingProgress = totalPages > 0 ? (currentPage / totalPages) * 100 : 0

  // Load and render PDF with high-quality rendering
  useEffect(() => {
    const loadPdf = async () => {
      setLoading(true)
      try {
        const pdfjsLib = await import('pdfjs-dist')
        pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`

        const pdf = await pdfjsLib.getDocument(pdfUrl).promise
        setPdfDoc(pdf)
        setTotalPages(pdf.numPages)

        // Pre-render first few pages for smooth experience
        await renderInitialPages(pdf)
        setLoading(false)
      } catch (error) {
        console.error('Error loading PDF:', error)
        setLoading(false)
      }
    }

    if (pdfUrl) {
      loadPdf()
    }
  }, [pdfUrl])

  // Render initial pages for smooth navigation
  const renderInitialPages = async (pdf: any) => {
    const pages: string[] = []
    const maxInitialPages = Math.min(5, pdf.numPages) // Render first 5 pages

    for (let pageNum = 1; pageNum <= maxInitialPages; pageNum++) {
      try {
        const pageDataUrl = await renderPageToDataUrl(pdf, pageNum)
        pages[pageNum - 1] = pageDataUrl
      } catch (error) {
        console.error(`Error rendering page ${pageNum}:`, error)
      }
    }

    setRenderedPages(pages)
  }

  // Render page to high-quality data URL
  const renderPageToDataUrl = async (pdf: any, pageNum: number): Promise<string> => {
    const page = await pdf.getPage(pageNum)
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')

    if (!context) throw new Error('Could not get canvas context')

    // Calculate optimal scale for current viewport
    const containerWidth = containerRef.current?.clientWidth || window.innerWidth
    const viewport = page.getViewport({ scale: 1, rotation })

    // High DPI scaling for crisp rendering
    const pixelRatio = window.devicePixelRatio || 1
    const baseScale = Math.min((containerWidth - 32) / viewport.width, 2.0) // Max 2x scale
    const finalScale = baseScale * scale * pixelRatio

    const scaledViewport = page.getViewport({ scale: finalScale, rotation })

    canvas.width = scaledViewport.width
    canvas.height = scaledViewport.height
    canvas.style.width = `${scaledViewport.width / pixelRatio}px`
    canvas.style.height = `${scaledViewport.height / pixelRatio}px`

    // Scale context for high DPI
    context.scale(pixelRatio, pixelRatio)

    const renderContext = {
      canvasContext: context,
      viewport: page.getViewport({ scale: finalScale / pixelRatio, rotation })
    }

    await page.render(renderContext).promise
    return canvas.toDataURL('image/png', 0.95)
  }

  // Lazy load page when needed
  const loadPageIfNeeded = async (pageNum: number) => {
    if (!renderedPages[pageNum - 1] && pdfDoc) {
      try {
        const pageDataUrl = await renderPageToDataUrl(pdfDoc, pageNum)
        setRenderedPages(prev => {
          const newPages = [...prev]
          newPages[pageNum - 1] = pageDataUrl
          return newPages
        })
      } catch (error) {
        console.error(`Error loading page ${pageNum}:`, error)
      }
    }
  }

  // Navigation with smooth page turning animation
  const goToPage = async (pageNum: number) => {
    if (pageNum >= 1 && pageNum <= totalPages && pageNum !== currentPage) {
      setIsPageTurning(true)

      // Load page if not already rendered
      await loadPageIfNeeded(pageNum)

      // Smooth transition
      setTimeout(() => {
        setCurrentPage(pageNum)
        setIsPageTurning(false)
      }, 150)
    }
  }

  const nextPage = () => goToPage(currentPage + 1)
  const prevPage = () => goToPage(currentPage - 1)

  // Zoom controls
  const zoomIn = () => {
    setScale(prev => Math.min(prev + 0.25, 3.0))
  }

  const zoomOut = () => {
    setScale(prev => Math.max(prev - 0.25, 0.5))
  }

  const resetZoom = () => {
    setScale(1.0)
  }

  const rotateClockwise = () => {
    setRotation(prev => (prev + 90) % 360)
  }

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
        e.preventDefault()
        prevPage()
      } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
        e.preventDefault()
        nextPage()
      } else if (e.key === '=' || e.key === '+') {
        e.preventDefault()
        zoomIn()
      } else if (e.key === '-') {
        e.preventDefault()
        zoomOut()
      } else if (e.key === '0') {
        e.preventDefault()
        resetZoom()
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [currentPage, totalPages])

  // Pre-load adjacent pages for smooth navigation
  useEffect(() => {
    if (pdfDoc && currentPage) {
      // Pre-load next and previous pages
      if (currentPage > 1) loadPageIfNeeded(currentPage - 1)
      if (currentPage < totalPages) loadPageIfNeeded(currentPage + 1)
    }
  }, [currentPage, pdfDoc, totalPages])

  return (
    <div
      ref={containerRef}
      className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col"
    >
      {/* Mobile-First Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="px-4 py-3">
          {/* Mobile Header */}
          <div className="flex items-center justify-between sm:hidden">
            <div className="flex-1 min-w-0">
              <h1 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                {bookTitle}
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Page {currentPage} of {totalPages}
              </p>
            </div>
            <div className="flex items-center space-x-2 ml-4">
              <Button variant="ghost" size="sm" onClick={zoomOut} disabled={scale <= 0.5}>
                <ZoomOut className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={zoomIn} disabled={scale >= 3.0}>
                <ZoomIn className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Desktop Header */}
          <div className="hidden sm:flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                {bookTitle}
              </h1>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                PDF Reader
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" onClick={zoomOut} disabled={scale <= 0.5}>
                <ZoomOut className="h-4 w-4" />
              </Button>
              <span className="text-sm text-gray-600 dark:text-gray-400 min-w-[60px] text-center">
                {Math.round(scale * 100)}%
              </span>
              <Button variant="ghost" size="sm" onClick={zoomIn} disabled={scale >= 3.0}>
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={rotateClockwise}>
                <RotateCw className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mt-3">
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
              <div
                className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                style={{ width: `${readingProgress}%` }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div
        ref={viewerRef}
        className="flex-1 flex items-center justify-center p-4 overflow-hidden"
      >
        {loading ? (
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading PDF...</p>
          </div>
        ) : (
          <div className="relative w-full h-full flex items-center justify-center">
            {/* Page Display with Animation */}
            <AnimatePresence mode="wait">
              <motion.div
                key={currentPage}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 1.05 }}
                transition={{ duration: 0.2, ease: "easeInOut" }}
                className="relative max-w-full max-h-full"
              >
                {renderedPages[currentPage - 1] ? (
                  <img
                    src={renderedPages[currentPage - 1]}
                    alt={`Page ${currentPage}`}
                    className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
                    style={{
                      transform: `rotate(${rotation}deg) scale(${scale})`,
                      transition: 'transform 0.3s ease'
                    }}
                    draggable={false}
                  />
                ) : (
                  <div className="flex items-center justify-center w-96 h-96 bg-gray-100 dark:bg-gray-800 rounded-lg">
                    <div className="flex flex-col items-center space-y-2">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      <p className="text-sm text-gray-500">Loading page {currentPage}...</p>
                    </div>
                  </div>
                )}
              </motion.div>
            </AnimatePresence>

            {/* Navigation Arrows - Hidden on mobile, shown on desktop */}
            <Button
              variant="ghost"
              size="lg"
              onClick={prevPage}
              disabled={currentPage === 1}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 hidden sm:flex opacity-70 hover:opacity-100 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
            >
              <ChevronLeft className="h-6 w-6" />
            </Button>

            <Button
              variant="ghost"
              size="lg"
              onClick={nextPage}
              disabled={currentPage === totalPages}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 hidden sm:flex opacity-70 hover:opacity-100 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
            >
              <ChevronRight className="h-6 w-6" />
            </Button>
          </div>
        )}
      </div>

      {/* Mobile Navigation Footer */}
      <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 sm:hidden">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={prevPage}
              disabled={currentPage === 1}
              className="flex items-center space-x-2"
            >
              <ChevronLeft className="h-4 w-4" />
              <span>Previous</span>
            </Button>

            <div className="flex items-center space-x-3">
              <Button variant="ghost" size="sm" onClick={resetZoom}>
                Reset
              </Button>
              <Button variant="ghost" size="sm" onClick={rotateClockwise}>
                <RotateCw className="h-4 w-4" />
              </Button>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={nextPage}
              disabled={currentPage === totalPages}
              className="flex items-center space-x-2"
            >
              <span>Next</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Desktop Bottom Controls */}
      <div className="hidden sm:block bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between max-w-6xl mx-auto">
            <Button
              variant="outline"
              onClick={prevPage}
              disabled={currentPage === 1}
              className="flex items-center space-x-2"
            >
              <ChevronLeft className="h-4 w-4" />
              <span>Previous</span>
            </Button>

            <div className="flex items-center space-x-6">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Page {currentPage} of {totalPages}
              </span>
              <Button variant="ghost" size="sm" onClick={resetZoom}>
                Reset View
              </Button>
            </div>

            <Button
              variant="outline"
              onClick={nextPage}
              disabled={currentPage === totalPages}
              className="flex items-center space-x-2"
            >
              <span>Next</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
