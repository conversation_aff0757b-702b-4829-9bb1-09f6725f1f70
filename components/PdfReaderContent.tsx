"use client"

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface PdfReaderContentProps {
  pdfUrl: string
  bookTitle: string
  fontSize: number
  fontFamily: string
  lineHeight: number
}

export function PdfReaderContent({ pdfUrl, bookTitle, fontSize, fontFamily, lineHeight }: PdfReaderContentProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [pdfDoc, setPdfDoc] = useState<any>(null)
  const [pageRendering, setPageRendering] = useState(false)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const loadPdf = async () => {
      try {
        // Dynamically import PDF.js to avoid SSR issues
        const pdfjsLib = await import('pdfjs-dist')
        
        // Set worker source
        pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`
        
        const pdf = await pdfjsLib.getDocument(pdfUrl).promise
        setPdfDoc(pdf)
        setTotalPages(pdf.numPages)
        
        // Render first page
        renderPage(1, pdf)
      } catch (error) {
        console.error('Error loading PDF:', error)
      }
    }

    if (pdfUrl) {
      loadPdf()
    }
  }, [pdfUrl])

  const renderPage = async (pageNum: number, pdf?: any) => {
    if (pageRendering) return
    
    setPageRendering(true)
    const pdfDocument = pdf || pdfDoc
    
    if (!pdfDocument || !canvasRef.current) {
      setPageRendering(false)
      return
    }

    try {
      const page = await pdfDocument.getPage(pageNum)
      const canvas = canvasRef.current
      const context = canvas.getContext('2d')
      
      // Calculate scale to fit the container width
      const containerWidth = canvas.parentElement?.clientWidth || 800
      const viewport = page.getViewport({ scale: 1 })
      const scale = (containerWidth - 64) / viewport.width // 64px for padding
      
      const scaledViewport = page.getViewport({ scale })
      
      canvas.height = scaledViewport.height
      canvas.width = scaledViewport.width
      
      const renderContext = {
        canvasContext: context,
        viewport: scaledViewport
      }
      
      await page.render(renderContext).promise
      setPageRendering(false)
    } catch (error) {
      console.error('Error rendering page:', error)
      setPageRendering(false)
    }
  }

  const goToPage = (pageNum: number) => {
    if (pageNum >= 1 && pageNum <= totalPages && pageNum !== currentPage) {
      setCurrentPage(pageNum)
      renderPage(pageNum)
    }
  }

  const nextPage = () => goToPage(currentPage + 1)
  const prevPage = () => goToPage(currentPage - 1)

  return (
    <div
      className="prose prose-lg max-w-none relative"
      style={{
        fontSize: `${fontSize}px`,
        fontFamily,
        lineHeight
      }}
    >
      {/* PDF Page Display - styled like EPUB reader */}
      <div className="flex flex-col items-center">
        {/* Page content container with EPUB-like styling */}
        <div className="w-full max-w-4xl bg-white rounded-lg shadow-sm border mb-6 relative overflow-hidden">
          {/* Page header with page info */}
          <div className="px-6 py-3 border-b border-gray-100 bg-gray-50">
            <div className="text-sm text-gray-600">
              {bookTitle} • Page {currentPage} of {totalPages}
            </div>
          </div>

          {/* PDF content area */}
          <div className="p-6 relative">
            <canvas
              ref={canvasRef}
              className="w-full h-auto rounded border border-gray-200"
              style={{
                maxWidth: '100%',
                backgroundColor: 'white',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
              }}
            />
            {pageRendering && (
              <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 rounded">
                <div className="flex items-center space-x-2 text-gray-600">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span>Loading page...</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Page Navigation - styled like EPUB chapter navigation */}
        {totalPages > 0 && (
          <div className="w-full max-w-4xl">
            <div className="border-t border-gray-200 bg-white rounded-b-lg p-4">
              <div className="flex items-center justify-between">
                <Button
                  variant="outline"
                  onClick={prevPage}
                  disabled={currentPage === 1}
                  className="flex items-center space-x-2 hover:bg-gray-50"
                >
                  <ChevronLeft className="h-4 w-4" />
                  <span>Previous</span>
                </Button>

                <div className="flex items-center space-x-4">
                  <div className="text-sm text-gray-600 font-medium">
                    Page {currentPage} of {totalPages}
                  </div>

                  {/* Progress bar like EPUB reader */}
                  <div className="w-32 h-1 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-blue-600 transition-all duration-300 rounded-full"
                      style={{ width: `${(currentPage / totalPages) * 100}%` }}
                    />
                  </div>
                </div>

                <Button
                  variant="outline"
                  onClick={nextPage}
                  disabled={currentPage === totalPages}
                  className="flex items-center space-x-2 hover:bg-gray-50"
                >
                  <span>Next</span>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Disclaimer - styled like EPUB reader notifications */}
      <div className="w-full max-w-4xl mt-6">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                <span className="text-blue-600 dark:text-blue-300 text-lg">📖</span>
              </div>
            </div>
            <div className="flex-1">
              <h4 className="text-base font-semibold text-blue-900 dark:text-blue-100 mb-2">
                PDF Reader Mode
              </h4>
              <p className="text-sm text-blue-800 dark:text-blue-200 leading-relaxed mb-3">
                You're viewing a PDF in our enhanced reader interface. While we've styled it to match our EPUB reader,
                some interactive features are limited in PDF mode.
              </p>
              <div className="text-xs text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-800/50 rounded px-3 py-2">
                <strong>💡 Pro Tip:</strong> For the full OnlyDiary reading experience with highlighting, comments,
                bookmarks, and social features, upload your book in EPUB format.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
