"use client"

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCw, Download, Share2 } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

interface PdfReaderContentProps {
  pdfUrl: string
  bookTitle: string
  fontSize: number
  fontFamily: string
  lineHeight: number
}

export function PdfReaderContent({ pdfUrl, bookTitle, fontSize, fontFamily, lineHeight }: PdfReaderContentProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [scale, setScale] = useState(1.0)
  const [loading, setLoading] = useState(true)
  const [pdfDoc, setPdfDoc] = useState<any>(null)
  const [pageImage, setPageImage] = useState<string>('')
  const [rendering, setRendering] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // Load PDF with working worker
  useEffect(() => {
    const loadPdf = async () => {
      setLoading(true)
      try {
        const pdfjsLib = await import('pdfjs-dist')

        // Use local worker or CDN fallback
        if (typeof window !== 'undefined') {
          pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js'
        }

        console.log('Loading PDF from:', pdfUrl)
        const pdf = await pdfjsLib.getDocument(pdfUrl).promise
        console.log('PDF loaded successfully, pages:', pdf.numPages)

        setPdfDoc(pdf)
        setTotalPages(pdf.numPages)

        // Render first page
        await renderPage(pdf, 1)
        setLoading(false)
      } catch (error) {
        console.error('Error loading PDF:', error)
        setLoading(false)
      }
    }

    if (pdfUrl) {
      loadPdf()
    }
  }, [pdfUrl])

  // Render specific page
  const renderPage = async (pdf: any, pageNum: number) => {
    if (rendering) return

    setRendering(true)
    try {
      console.log('Rendering page:', pageNum)
      const page = await pdf.getPage(pageNum)

      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')

      if (!context) {
        setRendering(false)
        return
      }

      // Calculate scale for responsive display
      const containerWidth = containerRef.current?.clientWidth || window.innerWidth - 32
      const viewport = page.getViewport({ scale: 1 })
      const baseScale = Math.min(containerWidth / viewport.width, 1.5)
      const finalScale = baseScale * scale

      const scaledViewport = page.getViewport({ scale: finalScale })

      canvas.width = scaledViewport.width
      canvas.height = scaledViewport.height

      const renderContext = {
        canvasContext: context,
        viewport: scaledViewport
      }

      await page.render(renderContext).promise

      // Convert to image
      const imageDataUrl = canvas.toDataURL('image/png', 0.9)
      setPageImage(imageDataUrl)
      setRendering(false)

      console.log('Page rendered successfully')
    } catch (error) {
      console.error('Error rendering page:', error)
      setRendering(false)
    }
  }

  // Navigation
  const goToPage = async (pageNum: number) => {
    if (pageNum >= 1 && pageNum <= totalPages && pageNum !== currentPage && pdfDoc) {
      setCurrentPage(pageNum)
      await renderPage(pdfDoc, pageNum)
    }
  }

  const nextPage = () => goToPage(currentPage + 1)
  const prevPage = () => goToPage(currentPage - 1)

  // Zoom controls
  const zoomIn = () => {
    setScale(prev => {
      const newScale = Math.min(prev + 0.25, 3.0)
      if (pdfDoc) renderPage(pdfDoc, currentPage)
      return newScale
    })
  }

  const zoomOut = () => {
    setScale(prev => {
      const newScale = Math.max(prev - 0.25, 0.5)
      if (pdfDoc) renderPage(pdfDoc, currentPage)
      return newScale
    })
  }

  const resetZoom = () => {
    setScale(1.0)
    if (pdfDoc) renderPage(pdfDoc, currentPage)
  }

  // Rotation (placeholder - can be implemented later)
  const rotateClockwise = () => {
    // Rotation functionality can be added here if needed
    console.log('Rotate functionality not implemented yet')
  }

  // Reading progress
  const readingProgress = totalPages > 0 ? (currentPage / totalPages) * 100 : 0

  return (
    <div
      ref={containerRef}
      className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col"
    >
      {/* Mobile-First Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="px-4 py-3">
          {/* Mobile Header */}
          <div className="flex items-center justify-between sm:hidden">
            <div className="flex-1 min-w-0">
              <h1 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                {bookTitle}
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Page {currentPage} of {totalPages}
              </p>
            </div>
            <div className="flex items-center space-x-2 ml-4">
              <Button variant="ghost" size="sm" onClick={zoomOut} disabled={scale <= 0.5}>
                <ZoomOut className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={zoomIn} disabled={scale >= 3.0}>
                <ZoomIn className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Desktop Header */}
          <div className="hidden sm:flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                {bookTitle}
              </h1>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                PDF Reader
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" onClick={zoomOut} disabled={scale <= 0.5}>
                <ZoomOut className="h-4 w-4" />
              </Button>
              <span className="text-sm text-gray-600 dark:text-gray-400 min-w-[60px] text-center">
                {Math.round(scale * 100)}%
              </span>
              <Button variant="ghost" size="sm" onClick={zoomIn} disabled={scale >= 3.0}>
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={rotateClockwise}>
                <RotateCw className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mt-3">
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
              <div
                className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                style={{ width: `${readingProgress}%` }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex items-center justify-center p-4 overflow-auto">
        {loading ? (
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading PDF...</p>
          </div>
        ) : (
          <div className="relative w-full h-full flex items-center justify-center">
            {/* Page Display */}
            <div className="relative max-w-full max-h-full">
              {rendering ? (
                <div className="flex items-center justify-center w-96 h-96 bg-gray-100 dark:bg-gray-800 rounded-lg">
                  <div className="flex flex-col items-center space-y-2">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p className="text-sm text-gray-500">Rendering page {currentPage}...</p>
                  </div>
                </div>
              ) : pageImage ? (
                <img
                  src={pageImage}
                  alt={`Page ${currentPage}`}
                  className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
                  draggable={false}
                />
              ) : (
                <div className="flex items-center justify-center w-96 h-96 bg-gray-100 dark:bg-gray-800 rounded-lg">
                  <p className="text-gray-500">Failed to load page</p>
                </div>
              )}
            </div>

            {/* Navigation Arrows - Hidden on mobile, shown on desktop */}
            <Button
              variant="ghost"
              size="lg"
              onClick={prevPage}
              disabled={currentPage === 1 || rendering}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 hidden sm:flex opacity-70 hover:opacity-100 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
            >
              <ChevronLeft className="h-6 w-6" />
            </Button>

            <Button
              variant="ghost"
              size="lg"
              onClick={nextPage}
              disabled={currentPage === totalPages || rendering}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 hidden sm:flex opacity-70 hover:opacity-100 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
            >
              <ChevronRight className="h-6 w-6" />
            </Button>
          </div>
        )}
      </div>

      {/* Mobile Navigation Footer */}
      <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 sm:hidden">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={prevPage}
              disabled={currentPage === 1 || rendering}
              className="flex items-center space-x-2"
            >
              <ChevronLeft className="h-4 w-4" />
              <span>Previous</span>
            </Button>

            <div className="flex items-center space-x-3">
              <Button variant="ghost" size="sm" onClick={resetZoom} disabled={rendering}>
                Reset
              </Button>
              <span className="text-xs text-gray-500">
                {Math.round(scale * 100)}%
              </span>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={nextPage}
              disabled={currentPage === totalPages || rendering}
              className="flex items-center space-x-2"
            >
              <span>Next</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Desktop Bottom Controls */}
      <div className="hidden sm:block bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between max-w-6xl mx-auto">
            <Button
              variant="outline"
              onClick={prevPage}
              disabled={currentPage === 1 || rendering}
              className="flex items-center space-x-2"
            >
              <ChevronLeft className="h-4 w-4" />
              <span>Previous</span>
            </Button>

            <div className="flex items-center space-x-6">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Page {currentPage} of {totalPages}
              </span>
              <Button variant="ghost" size="sm" onClick={resetZoom} disabled={rendering}>
                Reset View ({Math.round(scale * 100)}%)
              </Button>
            </div>

            <Button
              variant="outline"
              onClick={nextPage}
              disabled={currentPage === totalPages || rendering}
              className="flex items-center space-x-2"
            >
              <span>Next</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
