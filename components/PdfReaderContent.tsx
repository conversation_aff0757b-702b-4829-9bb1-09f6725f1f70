"use client"

import { useState, useEffect, useRef } from 'react'
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, BookOpen, Sun, Moon, Bookmark, Settings } from "lucide-react"

interface PdfReaderContentProps {
  pdfUrl: string
  bookTitle: string
  fontSize: number
  fontFamily: string
  lineHeight: number
}

export function PdfReaderContent({ pdfUrl, bookTitle, fontSize, fontFamily, lineHeight }: PdfReaderContentProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [theme, setTheme] = useState<'light' | 'sepia' | 'dark'>('sepia')
  const [readingProgress, setReadingProgress] = useState(0)
  const [showControls, setShowControls] = useState(true)
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [pdfDoc, setPdfDoc] = useState<any>(null)
  const [pageRendering, setPageRendering] = useState(false)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Theme classes for eBook styling
  const getThemeClasses = () => {
    switch (theme) {
      case 'light':
        return 'bg-white text-gray-900'
      case 'sepia':
        return 'bg-amber-50 text-amber-900'
      case 'dark':
        return 'bg-gray-900 text-gray-100'
      default:
        return 'bg-amber-50 text-amber-900'
    }
  }

  const getPageBackgroundClasses = () => {
    switch (theme) {
      case 'light':
        return 'bg-white'
      case 'sepia':
        return 'bg-amber-25'
      case 'dark':
        return 'bg-gray-800'
      default:
        return 'bg-amber-25'
    }
  }

  // Load PDF and get page count
  useEffect(() => {
    const loadPdf = async () => {
      try {
        const pdfjsLib = await import('pdfjs-dist')
        pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`

        const pdf = await pdfjsLib.getDocument(pdfUrl).promise
        setPdfDoc(pdf)
        setTotalPages(pdf.numPages)
        renderPage(1, pdf)
      } catch (error) {
        console.error('Error loading PDF:', error)
      }
    }

    if (pdfUrl) {
      loadPdf()
    }
  }, [pdfUrl])

  // Update reading progress
  useEffect(() => {
    if (totalPages > 0) {
      setReadingProgress((currentPage / totalPages) * 100)
    }
  }, [currentPage, totalPages])

  // Render specific page
  const renderPage = async (pageNum: number, pdf?: any) => {
    if (pageRendering) return

    setPageRendering(true)
    const pdfDocument = pdf || pdfDoc

    if (!pdfDocument || !canvasRef.current) {
      setPageRendering(false)
      return
    }

    try {
      const page = await pdfDocument.getPage(pageNum)
      const canvas = canvasRef.current
      const context = canvas.getContext('2d')

      if (!context) return

      // Calculate scale for book-like presentation
      const containerWidth = containerRef.current?.clientWidth || 800
      const maxWidth = Math.min(containerWidth - 120, 800) // Generous margins
      const viewport = page.getViewport({ scale: 1 })
      const scale = maxWidth / viewport.width

      const scaledViewport = page.getViewport({ scale })

      canvas.height = scaledViewport.height
      canvas.width = scaledViewport.width

      // Clear canvas
      context.clearRect(0, 0, canvas.width, canvas.height)

      const renderContext = {
        canvasContext: context,
        viewport: scaledViewport
      }

      await page.render(renderContext).promise
      setPageRendering(false)
    } catch (error) {
      console.error('Error rendering page:', error)
      setPageRendering(false)
    }
  }

  const goToPage = (pageNum: number) => {
    if (pageNum >= 1 && pageNum <= totalPages && pageNum !== currentPage) {
      setCurrentPage(pageNum)
      renderPage(pageNum)
    }
  }

  const nextPage = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1)
    }
  }

  const prevPage = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1)
    }
  }

  const toggleBookmark = () => {
    setIsBookmarked(!isBookmarked)
  }

  const cycleTheme = () => {
    const themes: ('light' | 'sepia' | 'dark')[] = ['light', 'sepia', 'dark']
    const currentIndex = themes.indexOf(theme)
    const nextIndex = (currentIndex + 1) % themes.length
    setTheme(themes[nextIndex])
  }

  return (
    <div
      ref={containerRef}
      className={`min-h-screen transition-colors duration-300 ${getThemeClasses()}`}
      onMouseMove={() => setShowControls(true)}
      onMouseLeave={() => setShowControls(false)}
    >

      {/* Top Controls Bar - Hidden/Shown on hover */}
      <div className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        showControls ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
      }`}>
        <div className={`${getThemeClasses()} border-b shadow-sm`}>
          <div className="max-w-6xl mx-auto px-6 py-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-lg font-semibold truncate max-w-md">{bookTitle}</h1>
              <span className="text-sm opacity-70">PDF Reader</span>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleBookmark}
                className={isBookmarked ? 'text-yellow-500' : ''}
              >
                <Bookmark className="h-4 w-4" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={cycleTheme}
                title="Change theme"
              >
                {theme === 'light' ? <Sun className="h-4 w-4" /> :
                 theme === 'sepia' ? <BookOpen className="h-4 w-4" /> :
                 <Moon className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Reading Area */}
      <div className="flex items-center justify-center min-h-screen px-6 py-20">
        <div className="max-w-4xl w-full">
          {/* Page Container with Book-like Styling */}
          <div className={`${getPageBackgroundClasses()} rounded-lg shadow-2xl p-12 mx-auto relative`}
               style={{
                 maxWidth: '800px',
                 minHeight: '600px',
                 boxShadow: '0 20px 60px rgba(0,0,0,0.1), 0 8px 25px rgba(0,0,0,0.08)'
               }}>

            {/* Page Content */}
            <div className="flex items-center justify-center h-full">
              {pageRendering ? (
                <div className="flex items-center space-x-3 text-gray-500">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-current"></div>
                  <span>Loading page...</span>
                </div>
              ) : (
                <canvas
                  ref={canvasRef}
                  className="max-w-full h-auto rounded shadow-sm"
                  style={{ backgroundColor: 'white' }}
                />
              )}
            </div>

            {/* Page Navigation Arrows */}
            <Button
              variant="ghost"
              size="sm"
              onClick={prevPage}
              disabled={currentPage === 1}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 opacity-70 hover:opacity-100"
            >
              <ChevronLeft className="h-6 w-6" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={nextPage}
              disabled={currentPage === totalPages}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 opacity-70 hover:opacity-100"
            >
              <ChevronRight className="h-6 w-6" />
            </Button>
          </div>

          {/* Page Info and Progress */}
          <div className="mt-6 text-center">
            <div className="text-sm opacity-70 mb-2">
              Page {currentPage} of {totalPages}
            </div>

            {/* Progress Bar */}
            <div className="w-64 h-1 bg-gray-300 dark:bg-gray-600 rounded-full mx-auto overflow-hidden">
              <div
                className="h-full bg-amber-600 transition-all duration-300 rounded-full"
                style={{ width: `${readingProgress}%` }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Controls */}
      <div className={`fixed bottom-0 left-0 right-0 z-50 transition-all duration-300 ${
        showControls ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'
      }`}>
        <div className={`${getThemeClasses()} border-t shadow-sm`}>
          <div className="max-w-6xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                onClick={prevPage}
                disabled={currentPage === 1}
                className="flex items-center space-x-2"
              >
                <ChevronLeft className="h-4 w-4" />
                <span>Previous</span>
              </Button>

              <div className="flex items-center space-x-4">
                <span className="text-sm font-medium">
                  {currentPage} / {totalPages}
                </span>
              </div>

              <Button
                variant="outline"
                onClick={nextPage}
                disabled={currentPage === totalPages}
                className="flex items-center space-x-2"
              >
                <span>Next</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Reading Mode Notice */}
      <div className="fixed bottom-20 right-6 z-40">
        <div className={`${getPageBackgroundClasses()} rounded-lg shadow-lg p-4 max-w-sm border`}>
          <div className="flex items-start space-x-3">
            <BookOpen className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-sm font-semibold mb-1">eBook-Style PDF Reader</h4>
              <p className="text-xs opacity-80 leading-relaxed">
                Enjoy a book-like reading experience with page-by-page navigation and comfortable themes.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
