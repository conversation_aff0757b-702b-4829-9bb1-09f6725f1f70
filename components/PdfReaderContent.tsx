"use client"

import { useState, useEffect, useRef } from 'react'
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCw, Download, Share2 } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

interface PdfReaderContentProps {
  pdfUrl: string
  bookTitle: string
  fontSize: number
  fontFamily: string
  lineHeight: number
}

export function PdfReaderContent({ pdfUrl, bookTitle, fontSize, fontFamily, lineHeight }: PdfReaderContentProps) {
  const [scale, setScale] = useState(1.0)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  // Simple zoom controls
  const zoomIn = () => {
    setScale(prev => Math.min(prev + 0.1, 2.0))
  }

  const zoomOut = () => {
    setScale(prev => Math.max(prev - 0.1, 0.5))
  }

  const resetZoom = () => {
    setScale(1.0)
  }

  return (
    <div
      ref={containerRef}
      className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col"
    >
      {/* Mobile-First Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="px-4 py-3">
          {/* Mobile Header */}
          <div className="flex items-center justify-between sm:hidden">
            <div className="flex-1 min-w-0">
              <h1 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                {bookTitle}
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Page {currentPage} of {totalPages}
              </p>
            </div>
            <div className="flex items-center space-x-2 ml-4">
              <Button variant="ghost" size="sm" onClick={zoomOut} disabled={scale <= 0.5}>
                <ZoomOut className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={zoomIn} disabled={scale >= 3.0}>
                <ZoomIn className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Desktop Header */}
          <div className="hidden sm:flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                {bookTitle}
              </h1>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                PDF Reader
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" onClick={zoomOut} disabled={scale <= 0.5}>
                <ZoomOut className="h-4 w-4" />
              </Button>
              <span className="text-sm text-gray-600 dark:text-gray-400 min-w-[60px] text-center">
                {Math.round(scale * 100)}%
              </span>
              <Button variant="ghost" size="sm" onClick={zoomIn} disabled={scale >= 3.0}>
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={rotateClockwise}>
                <RotateCw className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mt-3">
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
              <div
                className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                style={{ width: `${readingProgress}%` }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div
        ref={viewerRef}
        className="flex-1 flex items-center justify-center p-4 overflow-hidden"
      >
        {loading ? (
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading PDF...</p>
          </div>
        ) : (
          <div className="relative w-full h-full flex items-center justify-center">
            {/* Page Display with Animation */}
            <AnimatePresence mode="wait">
              <motion.div
                key={currentPage}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 1.05 }}
                transition={{ duration: 0.2, ease: "easeInOut" }}
                className="relative max-w-full max-h-full"
              >
                {renderedPages[currentPage - 1] ? (
                  <img
                    src={renderedPages[currentPage - 1]}
                    alt={`Page ${currentPage}`}
                    className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
                    style={{
                      transform: `rotate(${rotation}deg) scale(${scale})`,
                      transition: 'transform 0.3s ease'
                    }}
                    draggable={false}
                  />
                ) : (
                  <div className="flex items-center justify-center w-96 h-96 bg-gray-100 dark:bg-gray-800 rounded-lg">
                    <div className="flex flex-col items-center space-y-2">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      <p className="text-sm text-gray-500">Loading page {currentPage}...</p>
                    </div>
                  </div>
                )}
              </motion.div>
            </AnimatePresence>

            {/* Navigation Arrows - Hidden on mobile, shown on desktop */}
            <Button
              variant="ghost"
              size="lg"
              onClick={prevPage}
              disabled={currentPage === 1}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 hidden sm:flex opacity-70 hover:opacity-100 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
            >
              <ChevronLeft className="h-6 w-6" />
            </Button>

            <Button
              variant="ghost"
              size="lg"
              onClick={nextPage}
              disabled={currentPage === totalPages}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 hidden sm:flex opacity-70 hover:opacity-100 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
            >
              <ChevronRight className="h-6 w-6" />
            </Button>
          </div>
        )}
      </div>

      {/* Mobile Navigation Footer */}
      <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 sm:hidden">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={prevPage}
              disabled={currentPage === 1}
              className="flex items-center space-x-2"
            >
              <ChevronLeft className="h-4 w-4" />
              <span>Previous</span>
            </Button>

            <div className="flex items-center space-x-3">
              <Button variant="ghost" size="sm" onClick={resetZoom}>
                Reset
              </Button>
              <Button variant="ghost" size="sm" onClick={rotateClockwise}>
                <RotateCw className="h-4 w-4" />
              </Button>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={nextPage}
              disabled={currentPage === totalPages}
              className="flex items-center space-x-2"
            >
              <span>Next</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Desktop Bottom Controls */}
      <div className="hidden sm:block bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between max-w-6xl mx-auto">
            <Button
              variant="outline"
              onClick={prevPage}
              disabled={currentPage === 1}
              className="flex items-center space-x-2"
            >
              <ChevronLeft className="h-4 w-4" />
              <span>Previous</span>
            </Button>

            <div className="flex items-center space-x-6">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Page {currentPage} of {totalPages}
              </span>
              <Button variant="ghost" size="sm" onClick={resetZoom}>
                Reset View
              </Button>
            </div>

            <Button
              variant="outline"
              onClick={nextPage}
              disabled={currentPage === totalPages}
              className="flex items-center space-x-2"
            >
              <span>Next</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
