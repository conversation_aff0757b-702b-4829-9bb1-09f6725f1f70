"use client"

import { useState, useEffect, useRef } from 'react'
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface PdfReaderContentProps {
  pdfUrl: string
  bookTitle: string
  fontSize: number
  fontFamily: string
  lineHeight: number
}

export function PdfReaderContent({ pdfUrl, bookTitle, fontSize, fontFamily, lineHeight }: PdfReaderContentProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [loading, setLoading] = useState(true)
  const [pdfDoc, setPdfDoc] = useState<any>(null)
  const [pageImage, setPageImage] = useState<string>('')
  const [rendering, setRendering] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // Load PDF with working worker
  useEffect(() => {
    const loadPdf = async () => {
      setLoading(true)
      try {
        const pdfjsLib = await import('pdfjs-dist')

        // Disable worker for now - it will work but be slower
        pdfjsLib.GlobalWorkerOptions.workerSrc = false

        const pdf = await pdfjsLib.getDocument(pdfUrl).promise
        setPdfDoc(pdf)
        setTotalPages(pdf.numPages)

        // Render first page
        await renderPage(pdf, 1)
        setLoading(false)
      } catch (error) {
        console.error('Error loading PDF:', error)
        setLoading(false)
      }
    }

    if (pdfUrl) {
      loadPdf()
    }
  }, [pdfUrl])

  // Render one full page at a time
  const renderPage = async (pdf: any, pageNum: number) => {
    if (rendering) return

    setRendering(true)
    try {
      const page = await pdf.getPage(pageNum)
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')

      if (!context) {
        setRendering(false)
        return
      }

      // Get viewport and scale to fit container width
      const viewport = page.getViewport({ scale: 1 })
      const containerWidth = containerRef.current?.clientWidth || window.innerWidth - 64
      const scale = Math.min(containerWidth / viewport.width, 2.0) // Max 2x for quality

      const scaledViewport = page.getViewport({ scale })
      canvas.width = scaledViewport.width
      canvas.height = scaledViewport.height

      await page.render({
        canvasContext: context,
        viewport: scaledViewport
      }).promise

      // Convert to high-quality image
      setPageImage(canvas.toDataURL('image/png', 0.95))
      setRendering(false)
    } catch (error) {
      console.error('Error rendering page:', error)
      setRendering(false)
    }
  }

  // Simple navigation
  const nextPage = async () => {
    if (currentPage < totalPages && pdfDoc && !rendering) {
      const newPage = currentPage + 1
      setCurrentPage(newPage)
      await renderPage(pdfDoc, newPage)
    }
  }

  const prevPage = async () => {
    if (currentPage > 1 && pdfDoc && !rendering) {
      const newPage = currentPage - 1
      setCurrentPage(newPage)
      await renderPage(pdfDoc, newPage)
    }
  }

  // Reading progress
  const readingProgress = totalPages > 0 ? (currentPage / totalPages) * 100 : 0

  return (
    <div
      ref={containerRef}
      className="min-h-screen bg-white dark:bg-gray-900 flex flex-col"
    >
      {/* Simple Header */}
      <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
              {bookTitle}
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Page {currentPage} of {totalPages}
            </p>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mt-3">
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
            <div
              className="bg-blue-600 h-1 rounded-full transition-all duration-300"
              style={{ width: `${readingProgress}%` }}
            />
          </div>
        </div>
      </div>

      {/* Main Content - One Full PDF Page */}
      <div className="flex-1 flex items-center justify-center p-4">
        {loading ? (
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading PDF...</p>
          </div>
        ) : rendering ? (
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="text-gray-600 dark:text-gray-400">Rendering page {currentPage}...</p>
          </div>
        ) : pageImage ? (
          <div className="relative w-full h-full flex items-center justify-center">
            {/* Full PDF Page Display */}
            <img
              src={pageImage}
              alt={`Page ${currentPage}`}
              className="max-w-full max-h-full object-contain shadow-lg"
              draggable={false}
            />

            {/* Navigation Arrows */}
            <Button
              variant="ghost"
              size="lg"
              onClick={prevPage}
              disabled={currentPage === 1 || rendering}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 opacity-70 hover:opacity-100 bg-white/90 dark:bg-gray-800/90 shadow-lg"
            >
              <ChevronLeft className="h-6 w-6" />
            </Button>

            <Button
              variant="ghost"
              size="lg"
              onClick={nextPage}
              disabled={currentPage === totalPages || rendering}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 opacity-70 hover:opacity-100 bg-white/90 dark:bg-gray-800/90 shadow-lg"
            >
              <ChevronRight className="h-6 w-6" />
            </Button>
          </div>
        ) : (
          <div className="text-center">
            <p className="text-gray-500 dark:text-gray-400">Failed to load PDF page</p>
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 px-4 py-3">
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={prevPage}
            disabled={currentPage === 1 || rendering}
            className="flex items-center space-x-2"
          >
            <ChevronLeft className="h-4 w-4" />
            <span>Previous</span>
          </Button>

          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {currentPage} / {totalPages}
          </span>

          <Button
            variant="outline"
            onClick={nextPage}
            disabled={currentPage === totalPages || rendering}
            className="flex items-center space-x-2"
          >
            <span>Next</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
