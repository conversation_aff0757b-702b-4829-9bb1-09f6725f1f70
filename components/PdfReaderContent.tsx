"use client"

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface PdfReaderContentProps {
  pdfUrl: string
  bookTitle: string
  fontSize: number
  fontFamily: string
  lineHeight: number
}

export function PdfReaderContent({ pdfUrl, bookTitle, fontSize, fontFamily, lineHeight }: PdfReaderContentProps) {
  const [totalPages, setTotalPages] = useState(0)
  const [pdfDoc, setPdfDoc] = useState<any>(null)
  const [renderedPages, setRenderedPages] = useState<HTMLCanvasElement[]>([])
  const [loading, setLoading] = useState(true)
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const loadPdf = async () => {
      setLoading(true)
      try {
        // Dynamically import PDF.js to avoid SSR issues
        const pdfjsLib = await import('pdfjs-dist')

        // Set worker source
        pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`

        const pdf = await pdfjsLib.getDocument(pdfUrl).promise
        setPdfDoc(pdf)
        setTotalPages(pdf.numPages)

        // Render all pages for vertical scrolling
        await renderAllPages(pdf)
      } catch (error) {
        console.error('Error loading PDF:', error)
        setLoading(false)
      }
    }

    if (pdfUrl) {
      loadPdf()
    }
  }, [pdfUrl])

  const renderAllPages = async (pdf: any) => {
    const pages: HTMLCanvasElement[] = []
    const containerWidth = containerRef.current?.clientWidth || 800

    try {
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const page = await pdf.getPage(pageNum)
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')

        if (!context) continue

        // Calculate scale to fit the container width
        const viewport = page.getViewport({ scale: 1 })
        const scale = (containerWidth - 64) / viewport.width // 64px for padding
        const scaledViewport = page.getViewport({ scale })

        canvas.height = scaledViewport.height
        canvas.width = scaledViewport.width
        canvas.className = 'w-full h-auto mb-6 rounded border border-gray-200 shadow-sm'
        canvas.style.backgroundColor = 'white'

        const renderContext = {
          canvasContext: context,
          viewport: scaledViewport
        }

        await page.render(renderContext).promise
        pages.push(canvas)
      }

      setRenderedPages(pages)
      setLoading(false)
    } catch (error) {
      console.error('Error rendering pages:', error)
      setLoading(false)
    }
  }

  return (
    <div
      ref={containerRef}
      className="prose prose-lg max-w-none relative"
      style={{
        fontSize: `${fontSize}px`,
        fontFamily,
        lineHeight
      }}
    >
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-3 text-gray-600">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span>Loading PDF pages...</span>
          </div>
        </div>
      ) : (
        <>
          {/* All PDF Pages displayed vertically like EPUB reader */}
          <div className="space-y-8">
            {renderedPages.map((canvas, index) => (
              <div key={index} className="flex flex-col items-center">
                {/* Page number indicator */}
                <div className="text-xs text-gray-500 mb-2 font-medium">
                  Page {index + 1}
                </div>

                {/* Page content */}
                <div
                  className="w-full max-w-4xl"
                  dangerouslySetInnerHTML={{
                    __html: canvas.outerHTML
                  }}
                />
              </div>
            ))}
          </div>

          {/* Reading progress indicator */}
          {totalPages > 0 && (
            <div className="mt-8 text-center text-sm text-gray-500">
              {totalPages} pages • PDF format
            </div>
          )}
        </>
      )}

      {/* Enhanced Disclaimer - styled like EPUB reader notifications */}
      <div className="mt-8 max-w-4xl mx-auto">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                <span className="text-blue-600 dark:text-blue-300 text-lg">📖</span>
              </div>
            </div>
            <div className="flex-1">
              <h4 className="text-base font-semibold text-blue-900 dark:text-blue-100 mb-2">
                PDF Reader Mode
              </h4>
              <p className="text-sm text-blue-800 dark:text-blue-200 leading-relaxed mb-3">
                You're viewing a PDF in our enhanced reader interface. The content is displayed exactly as stored in the PDF,
                with vertical scrolling like our EPUB reader.
              </p>
              <div className="text-xs text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-800/50 rounded px-3 py-2">
                <strong>💡 Pro Tip:</strong> For the full OnlyDiary reading experience with highlighting, comments,
                bookmarks, and social features, upload your book in EPUB format.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
