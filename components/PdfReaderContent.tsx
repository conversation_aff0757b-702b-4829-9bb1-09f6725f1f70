"use client"

import { useState, useEffect, useRef } from 'react'
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface PdfReaderContentProps {
  pdfUrl: string
  bookTitle: string
  fontSize: number
  fontFamily: string
  lineHeight: number
}

export function PdfReaderContent({ pdfUrl, bookTitle, fontSize, fontFamily, lineHeight }: PdfReaderContentProps) {
  return (
    <div
      className="prose prose-lg max-w-none relative"
      style={{
        fontSize: `${fontSize}px`,
        fontFamily,
        lineHeight
      }}
    >
      {/* Simple PDF iframe that looks like EPUB reader */}
      <div className="space-y-6">
        <div className="w-full max-w-4xl mx-auto bg-white rounded-lg shadow-sm border overflow-hidden">
          {/* Header like EPUB reader */}
          <div className="px-6 py-3 border-b border-gray-100 bg-gray-50">
            <div className="text-sm text-gray-600 font-medium">
              {bookTitle} • PDF Document
            </div>
          </div>

          {/* PDF content area */}
          <div className="p-6">
            <iframe
              src={pdfUrl}
              className="w-full border-0 rounded"
              style={{
                height: '80vh',
                backgroundColor: 'white'
              }}
              title={bookTitle}
            />
          </div>
        </div>
      </div>

      {/* Enhanced Disclaimer - styled like EPUB reader notifications */}
      <div className="mt-8 max-w-4xl mx-auto">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                <span className="text-blue-600 dark:text-blue-300 text-lg">📖</span>
              </div>
            </div>
            <div className="flex-1">
              <h4 className="text-base font-semibold text-blue-900 dark:text-blue-100 mb-2">
                PDF Reader Mode
              </h4>
              <p className="text-sm text-blue-800 dark:text-blue-200 leading-relaxed mb-3">
                You're viewing a PDF in our enhanced reader interface. The content is displayed exactly as stored in the PDF,
                with vertical scrolling like our EPUB reader.
              </p>
              <div className="text-xs text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-800/50 rounded px-3 py-2">
                <strong>💡 Pro Tip:</strong> For the full OnlyDiary reading experience with highlighting, comments,
                bookmarks, and social features, upload your book in EPUB format.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
