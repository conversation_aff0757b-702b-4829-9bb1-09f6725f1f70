"use client"

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface PdfReaderContentProps {
  pdfUrl: string
  bookTitle: string
  fontSize: number
  fontFamily: string
  lineHeight: number
  onLoadComplete?: () => void
}

export function PdfReaderContent({ pdfUrl, bookTitle, fontSize, fontFamily, lineHeight, onLoadComplete }: PdfReaderContentProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [loading, setLoading] = useState(true)
  const [pdfDoc, setPdfDoc] = useState<any>(null)
  const [pageImage, setPageImage] = useState<string>('')
  const [rendering, setRendering] = useState(false)
  const [isBlankPage, setIsBlankPage] = useState(false)
  const [readingProgress, setReadingProgress] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)

  // Production-ready PDF loading with local worker
  useEffect(() => {
    const loadPdf = async () => {
      setLoading(true)
      try {
        const pdfjsLib = await import('pdfjs-dist')

        // Use local worker file for maximum performance and reliability
        pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js'

        // Force clear any cached worker settings
        if (pdfjsLib.GlobalWorkerOptions.workerPort) {
          pdfjsLib.GlobalWorkerOptions.workerPort.terminate()
          pdfjsLib.GlobalWorkerOptions.workerPort = null
        }

        console.log('Loading PDF with local worker...')
        const pdf = await pdfjsLib.getDocument(pdfUrl).promise
        console.log(`PDF loaded successfully: ${pdf.numPages} pages`)

        setPdfDoc(pdf)
        setTotalPages(pdf.numPages)

        // Render first page
        await renderPage(pdf, 1)
        setLoading(false)

        // Notify parent that loading is complete
        if (onLoadComplete) {
          onLoadComplete()
        }
      } catch (error) {
        console.error('Error loading PDF:', error)
        setLoading(false)
      }
    }

    if (pdfUrl) {
      loadPdf()
    }
  }, [pdfUrl])

  // Simple cleanup
  useEffect(() => {
    return () => {
      if (pdfDoc) {
        pdfDoc.destroy()
      }
    }
  }, [pdfDoc])

  // Detect if a page is mostly blank
  const detectBlankPage = async (context: CanvasRenderingContext2D, width: number, height: number): Promise<boolean> => {
    const imageData = context.getImageData(0, 0, width, height)
    const data = imageData.data
    let nonWhitePixels = 0
    const totalPixels = width * height

    // Sample every 10th pixel for performance
    for (let i = 0; i < data.length; i += 40) { // 4 bytes per pixel * 10
      const r = data[i]
      const g = data[i + 1]
      const b = data[i + 2]

      // Consider pixel non-white if it's not close to white
      if (r < 240 || g < 240 || b < 240) {
        nonWhitePixels++
      }
    }

    const sampledPixels = totalPixels / 10
    const contentRatio = nonWhitePixels / sampledPixels

    // Page is blank if less than 1% has content
    return contentRatio < 0.01
  }

  // Update reading progress and save it
  useEffect(() => {
    if (totalPages > 0) {
      const progress = (currentPage / totalPages) * 100
      setReadingProgress(progress)

      // Save progress to localStorage for persistence
      const progressKey = `pdf-progress-${bookTitle.replace(/[^a-zA-Z0-9]/g, '-')}`
      localStorage.setItem(progressKey, JSON.stringify({
        currentPage,
        totalPages,
        lastRead: new Date().toISOString(),
        progress: Math.round(progress)
      }))
    }
  }, [currentPage, totalPages, bookTitle])

  // Load saved progress on mount
  useEffect(() => {
    if (totalPages > 0) {
      const progressKey = `pdf-progress-${bookTitle.replace(/[^a-zA-Z0-9]/g, '-')}`
      const savedProgress = localStorage.getItem(progressKey)

      if (savedProgress) {
        try {
          const { currentPage: savedPage } = JSON.parse(savedProgress)
          if (savedPage > 1 && savedPage <= totalPages) {
            setCurrentPage(savedPage)
            if (pdfDoc) {
              renderPage(pdfDoc, savedPage)
            }
          }
        } catch (error) {
          console.error('Error loading saved progress:', error)
        }
      }
    }
  }, [totalPages, pdfDoc])

  // Simple, reliable page rendering - one page at a time
  const renderPage = async (pdf: any, pageNum: number) => {
    if (rendering) return

    setRendering(true)
    console.log(`Rendering page ${pageNum}...`)

    try {
      const page = await pdf.getPage(pageNum)
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')

      if (!context) {
        console.error('Could not get canvas context')
        setRendering(false)
        return
      }

      // Simple scaling - fit to container width
      const viewport = page.getViewport({ scale: 1 })
      const containerWidth = containerRef.current?.clientWidth || window.innerWidth - 64
      const scale = Math.min(containerWidth / viewport.width, 1.5) // Max 1.5x for performance

      const scaledViewport = page.getViewport({ scale })
      canvas.width = scaledViewport.width
      canvas.height = scaledViewport.height

      // Render the page
      await page.render({
        canvasContext: context,
        viewport: scaledViewport
      }).promise

      // Convert to image and display
      const imageDataUrl = canvas.toDataURL('image/png', 0.9)
      setPageImage(imageDataUrl)

      // Detect if page is mostly blank
      const isBlank = await detectBlankPage(context, canvas.width, canvas.height)
      setIsBlankPage(isBlank)

      setRendering(false)
      console.log(`Page ${pageNum} rendered successfully${isBlank ? ' (blank page detected)' : ''}`)

    } catch (error) {
      console.error(`Error rendering page ${pageNum}:`, error)
      setRendering(false)
    }
  }

  // Simple, reliable navigation
  const nextPage = () => {
    if (currentPage < totalPages && pdfDoc && !rendering) {
      const newPage = currentPage + 1
      console.log(`Going to page ${newPage}`)
      setCurrentPage(newPage)
      renderPage(pdfDoc, newPage)
    }
  }

  const prevPage = () => {
    if (currentPage > 1 && pdfDoc && !rendering) {
      const newPage = currentPage - 1
      console.log(`Going to page ${newPage}`)
      setCurrentPage(newPage)
      renderPage(pdfDoc, newPage)
    }
  }

  // Reading progress is already defined as state above

  return (
    <div
      ref={containerRef}
      className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 flex flex-col"
    >
      {/* Mobile-Optimized Header */}
      <div className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border-b border-slate-200/50 dark:border-slate-700/50 sticky top-0 z-50 shadow-sm">
        <div className="px-3 py-2 sm:px-4 sm:py-4">
          {/* Mobile: Compact Header */}
          <div className="sm:hidden">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg">
                  <span className="text-white text-sm">📖</span>
                </div>
                <div className="min-w-0 flex-1">
                  <h1 className="text-sm font-bold text-slate-900 dark:text-white truncate">
                    {bookTitle}
                  </h1>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm font-semibold text-slate-800 dark:text-slate-200">
                  {currentPage}/{totalPages}
                </div>
                <div className="text-xs text-slate-500 dark:text-slate-400">
                  {Math.round(readingProgress)}%
                </div>
              </div>
            </div>

            {/* Mobile Progress Bar */}
            <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-1.5 overflow-hidden shadow-inner">
              <div
                className="bg-gradient-to-r from-blue-500 to-purple-600 h-full rounded-full transition-all duration-500 ease-out"
                style={{ width: `${readingProgress}%` }}
              />
            </div>
          </div>

          {/* Desktop: Original Beautiful Header */}
          <div className="hidden sm:block">
            <div className="max-w-4xl mx-auto px-4 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                    <span className="text-white text-lg">📖</span>
                  </div>
                  <div>
                    <h1 className="text-lg font-bold text-slate-900 dark:text-white">
                      {bookTitle}
                    </h1>
                    <div className="flex items-center space-x-2 text-sm text-slate-600 dark:text-slate-400">
                      <span>Page {currentPage} of {totalPages}</span>
                      <span>•</span>
                      <span>{Math.round(readingProgress)}% complete</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Progress Bar */}
              <div className="mt-4">
                <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2 overflow-hidden shadow-inner">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-purple-600 h-full rounded-full transition-all duration-500 ease-out shadow-sm"
                    style={{ width: `${readingProgress}%` }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Brilliant PDF Display */}
      <div className="flex-1 flex items-center justify-center p-6">
        {loading ? (
          <div className="flex flex-col items-center space-y-6">
            <div className="relative">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-slate-200 border-t-blue-600"></div>
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 opacity-20 animate-pulse"></div>
            </div>
            <div className="text-center">
              <p className="text-lg font-medium text-slate-700 dark:text-slate-300">Loading PDF...</p>
              <p className="text-sm text-slate-500 dark:text-slate-400">Preparing your reading experience</p>
            </div>
          </div>
        ) : rendering ? (
          <div className="flex flex-col items-center space-y-6">
            <div className="relative">
              <div className="animate-spin rounded-full h-10 w-10 border-4 border-slate-200 border-t-purple-600"></div>
            </div>
            <div className="text-center">
              <p className="text-lg font-medium text-slate-700 dark:text-slate-300">Rendering page {currentPage}...</p>
              <p className="text-sm text-slate-500 dark:text-slate-400">Please wait a moment</p>
            </div>
          </div>
        ) : pageImage ? (
          <div className="relative w-full h-full flex items-center justify-center">
            {/* PDF Page Container */}
            <div className="relative max-w-4xl w-full">
              {/* Page Shadow Effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-800 rounded-lg transform translate-x-1 translate-y-1 -z-10"></div>

              {/* Main Page */}
              <div className="bg-white dark:bg-slate-100 rounded-lg shadow-2xl overflow-hidden border border-slate-200 dark:border-slate-300">
                <img
                  src={pageImage}
                  alt={`Page ${currentPage}`}
                  className="w-full h-auto object-contain"
                  draggable={false}
                />
              </div>

              {/* Blank Page Overlay */}
              {isBlankPage && (
                <div className="absolute inset-0 flex items-center justify-center bg-white/95 dark:bg-slate-100/95 rounded-lg">
                  <div className="text-center p-8 max-w-md">
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-white text-2xl">📄</span>
                    </div>
                    <h3 className="text-lg font-semibold text-slate-800 mb-2">Blank Page</h3>
                    <p className="text-sm text-slate-600 mb-4">
                      This page appears to be blank or contains minimal content. This is common in book formatting.
                    </p>
                    <div className="flex items-center justify-center space-x-2 text-xs text-slate-500">
                      <span>Use the arrows or swipe to continue reading</span>
                      <span>→</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Enhanced Navigation Arrows */}
            <Button
              variant="ghost"
              size="lg"
              onClick={prevPage}
              disabled={currentPage === 1 || rendering}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 opacity-80 hover:opacity-100 bg-white/95 dark:bg-slate-800/95 shadow-xl border border-slate-200 dark:border-slate-600 backdrop-blur-sm transition-all duration-200 hover:scale-105"
            >
              <ChevronLeft className="h-6 w-6" />
            </Button>

            <Button
              variant="ghost"
              size="lg"
              onClick={nextPage}
              disabled={currentPage === totalPages || rendering}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 opacity-80 hover:opacity-100 bg-white/95 dark:bg-slate-800/95 shadow-xl border border-slate-200 dark:border-slate-600 backdrop-blur-sm transition-all duration-200 hover:scale-105"
            >
              <ChevronRight className="h-6 w-6" />
            </Button>
          </div>
        ) : (
          <div className="text-center p-8">
            <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-red-600 text-2xl">⚠️</span>
            </div>
            <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-2">Failed to Load Page</h3>
            <p className="text-sm text-slate-600 dark:text-slate-400">There was an issue loading this PDF page.</p>
          </div>
        )}
      </div>

      {/* Mobile-Optimized Bottom Navigation */}
      <div className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border-t border-slate-200/50 dark:border-slate-700/50 shadow-lg">
        <div className="px-3 py-3 sm:px-6 sm:py-4">
          {/* Mobile: Simplified Navigation */}
          <div className="sm:hidden">
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                onClick={prevPage}
                disabled={currentPage === 1 || rendering}
                className="flex items-center space-x-1 bg-white/90 dark:bg-slate-800/90 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700 transition-all duration-200 shadow-sm px-4 py-2"
              >
                <ChevronLeft className="h-4 w-4" />
                <span className="text-sm font-medium">Prev</span>
              </Button>

              <div className="text-center flex-1 mx-4">
                <div className="text-base font-bold text-slate-800 dark:text-slate-200">
                  {currentPage} / {totalPages}
                </div>
                <div className="text-xs text-slate-500 dark:text-slate-400">
                  {Math.round(readingProgress)}% complete
                </div>
              </div>

              <Button
                variant="outline"
                onClick={nextPage}
                disabled={currentPage === totalPages || rendering}
                className="flex items-center space-x-1 bg-white/90 dark:bg-slate-800/90 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700 transition-all duration-200 shadow-sm px-4 py-2"
              >
                <span className="text-sm font-medium">Next</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Desktop: Original Beautiful Navigation */}
          <div className="hidden sm:block">
            <div className="max-w-4xl mx-auto px-6 py-4">
              <div className="flex items-center justify-between">
                <Button
                  variant="outline"
                  onClick={prevPage}
                  disabled={currentPage === 1 || rendering}
                  className="flex items-center space-x-2 bg-white/80 dark:bg-slate-800/80 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700 transition-all duration-200 shadow-sm"
                >
                  <ChevronLeft className="h-4 w-4" />
                  <span>Previous</span>
                </Button>

                <div className="flex items-center space-x-4">
                  <div className="text-center">
                    <div className="text-lg font-bold text-slate-800 dark:text-slate-200">
                      {currentPage} / {totalPages}
                    </div>
                    <div className="text-xs text-slate-500 dark:text-slate-400">
                      {Math.round(readingProgress)}% complete
                    </div>
                  </div>

                  {/* Quick Progress Dots */}
                  <div className="hidden md:flex items-center space-x-1">
                    {Array.from({ length: Math.min(totalPages, 10) }, (_, i) => {
                      const pageNum = Math.floor((i / 9) * (totalPages - 1)) + 1
                      const isActive = Math.abs(pageNum - currentPage) <= totalPages / 10
                      return (
                        <div
                          key={i}
                          className={`w-2 h-2 rounded-full transition-all duration-200 ${
                            isActive
                              ? 'bg-gradient-to-r from-blue-500 to-purple-600 scale-125'
                              : 'bg-slate-300 dark:bg-slate-600'
                          }`}
                        />
                      )
                    })}
                  </div>
                </div>

                <Button
                  variant="outline"
                  onClick={nextPage}
                  disabled={currentPage === totalPages || rendering}
                  className="flex items-center space-x-2 bg-white/80 dark:bg-slate-800/80 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700 transition-all duration-200 shadow-sm"
                >
                  <span>Next</span>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
