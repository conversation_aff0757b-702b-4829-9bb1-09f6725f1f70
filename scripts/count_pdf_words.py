#!/usr/bin/env python3
"""
Simple PDF word counter using PyMuPDF
Usage: python count_pdf_words.py <pdf_file_path>
"""

import sys
import fitz  # PyMuPDF
import re
import json

def count_words_in_pdf(pdf_path):
    try:
        # Open the PDF
        doc = fitz.open(pdf_path)
        
        total_text = ""
        
        # Extract text from all pages
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text = page.get_text()
            total_text += text + " "
        
        doc.close()
        
        # Clean the text
        # Remove extra whitespace
        clean_text = re.sub(r'\s+', ' ', total_text)
        
        # Remove non-word characters but keep apostrophes and hyphens
        clean_text = re.sub(r'[^\w\s\'-]', ' ', clean_text)
        
        # Split into words and filter
        words = clean_text.split()
        words = [word for word in words if len(word) > 1 and re.search(r'[a-zA-Z]', word)]
        
        word_count = len(words)
        reading_time = max(1, word_count // 200)
        
        return {
            "success": True,
            "word_count": word_count,
            "reading_time_minutes": reading_time,
            "text_preview": clean_text[:200] + "..." if len(clean_text) > 200 else clean_text
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python count_pdf_words.py <pdf_file_path>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    result = count_words_in_pdf(pdf_path)
    print(json.dumps(result))
